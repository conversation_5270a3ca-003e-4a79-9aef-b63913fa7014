@import 'tailwindcss';

@theme {
  /* Screens */
  --breakpoint-xs: 390px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1440px;

  /* Font weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font families */
  --font-family-inter: Inter, sans-serif;

  /* Border radius */
  --radius-xxl: 1.5rem;
  --radius-1xl: 0.875rem;

  /* Box shadows */
  --shadow-container: 0px 5px 20px 0px rgba(42, 40, 135, 0.05);
  --shadow-hover: 0px 6px 15px 0px rgba(42, 40, 135, 0.07);
  --shadow-inset-dialog-gray: 0px 0px 0px 1px var(--color-neutral-200) inset;

  /* Transition delays */
  --transition-delay-3000: 3000ms;

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-loader: loader 0.6s infinite alternate;
  --animate-fade-out: fade-out 2s ease 0s 1 normal forwards;
  --animate-ride-in-right: ride-in-right 2s ease 0s 1 normal forwards;
  --animate-ride-in-left: ride-in-left 2s ease 0s 1 normal forwards;
  --animate-fade: fade 1.25s ease-out forwards;
  --animate-indicator: indicator 2s ease-in-out infinite;
  --animate-pulse-and-pump: pulse-and-pump 0.5s
    cubic-bezier(0.21, 0.35, 0.44, 0.99) infinite;
  --animate-pulse: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.4s infinite;
  --animate-pulse-2: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.7s
    infinite;
  --animate-pulse-3: slow-fade-pulse 2s cubic-bezier(0, 0.85, 0, 0.75) 0.9s
    infinite;
  --animate-rotate-user: rotate-user 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-user: rotate-reverse-user 23.4s ease-in-out 3.9s
    infinite;
  --animate-rotate-euro: rotate-euro 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-euro: rotate-reverse-euro 23.4s ease-in-out 3.9s
    infinite;
  --animate-rotate-bank: rotate-bank 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-bank: rotate-reverse-bank 23.4s ease-in-out 3.9s
    infinite;
  --animate-pulse-opacity: pulse-opacity 2s cubic-bezier(0.4, 0, 0.6, 1)
    infinite;
  --animate-lds-spinner: lds-spinner 1.2s linear infinite;
}

/* Keyframes for animations */
@keyframes loader {
  to {
    opacity: 0.1;
    transform: translate3d(0, -0.25rem, 0);
  }
}

@keyframes lds-spinner {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes ride-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes ride-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade {
  0% {
    opacity: 1;
    height: 100%;
  }
  50% {
    opacity: 1;
    height: 100%;
  }
  100% {
    opacity: 0;
    height: 0;
  }
}

@keyframes indicator {
  0% {
    width: 0%;
    height: 0%;
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  100% {
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes pulse-and-pump {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: translate(-50%, -50%) scale(1);
  }
  6.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: translate(-50%, -50%) scale(0.95);
  }
  12.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: translate(-50%, -50%) scale(0.9);
  }
  18.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: translate(-50%, -50%) scale(0.85);
  }
  25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: translate(-50%, -50%) scale(0.8);
  }
  31.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 0%);
    transform: translate(-50%, -50%) scale(0.75);
  }
  37.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: translate(-50%, -50%) scale(0.8);
  }
  43.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: translate(-50%, -50%) scale(0.85);
  }
  50% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: translate(-50%, -50%) scale(0.9);
  }
  56.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: translate(-50%, -50%) scale(0.95);
  }
  62.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: translate(-50%, -50%) scale(1);
  }
  68.75%,
  75%,
  81.25%,
  87.5%,
  93.75%,
  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

@keyframes pulse-opacity {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
  }

  /* This is needed to fix the z-index of the popover */
  [data-radix-popper-content-wrapper] {
    z-index: 300 !important;
  }
}

@layer base {
  * {
    /* @apply border-border; */
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* Our configs */
/* Color design system */
/* Link => https://www.figma.com/file/qRNfEkalc67TUhyFm6AjFf/ESTO-Design-System?type=design&node-id=1-6481&mode=design&t=cv07M8IvBhRx2JAL-0 */

@layer base {
  html,
  body,
  #root {
    /* font-inter */
    height: 100%;
  }

  :root {
    /* Primary */
    --color-primary-brand-02: #3300ff;
    --color-primary-black: #000000;
    --color-primary-white: #ffffff;
    /* System */
    --color-system-green: #38cf86;
    --color-system-yellow: #ead40d;
    --color-system-orange: #ea770d;
    --color-system-red: #ea350d;
    /* Neutral */
    --color-neutral-900: #111827;
    --color-neutral-800: #1f2937;
    --color-neutral-700: #374151;
    --color-neutral-600: #4b5563;
    --color-neutral-500: #6b7280;
    --color-neutral-400: #9ca3af;
    --color-neutral-300: #d1d5db;
    --color-neutral-200: #e5e7eb;
    --color-neutral-100: #f3f4f6;
    --color-neutral-50: #f9fafb;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 0.2s;
  }
  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .underline-from-font {
    text-underline-position: from-font;
  }
}
