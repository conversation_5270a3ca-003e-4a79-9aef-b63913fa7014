@import 'tailwindcss';

@theme {
  /* Screens */
  --breakpoint-xs: 390px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1440px;

  /* Font weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font families */
  --font-family-inter: Inter, sans-serif;

  /* Border radius */
  --radius-xxl: 1.5rem;
  --radius-1xl: 0.875rem;

  /* Box shadows */
  --shadow-container: 0px 5px 20px 0px rgba(42, 40, 135, 0.05);
  --shadow-hover: 0px 6px 15px 0px rgba(42, 40, 135, 0.07);
  --shadow-inset-dialog-gray: 0px 0px 0px 1px var(--color-neutral-200) inset;

  /* Container */
  --container-center: true;
  --container-padding: 2rem;

  /* Transition delays */
  --transition-delay-3000: 3000ms;

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-loader: loader 0.6s infinite alternate;
  --animate-fade-out: fade-out 2s ease 0s 1 normal forwards;
  --animate-ride-in-right: ride-in-right 2s ease 0s 1 normal forwards;
  --animate-ride-in-left: ride-in-left 2s ease 0s 1 normal forwards;
  --animate-fade: fade 1.25s ease-out forwards;
  --animate-indicator: indicator 2s ease-in-out infinite;
  --animate-pulse-and-pump: pulse-and-pump 0.5s
    cubic-bezier(0.21, 0.35, 0.44, 0.99) infinite;
  --animate-pulse: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.4s infinite;
  --animate-pulse-2: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.7s
    infinite;
  --animate-pulse-3: slow-fade-pulse 2s cubic-bezier(0, 0.85, 0, 0.75) 0.9s
    infinite;
  --animate-rotate-user: rotate-user 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-user: rotate-reverse-user 23.4s ease-in-out 3.9s
    infinite;
  --animate-rotate-euro: rotate-euro 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-euro: rotate-reverse-euro 23.4s ease-in-out 3.9s
    infinite;
  --animate-rotate-bank: rotate-bank 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-bank: rotate-reverse-bank 23.4s ease-in-out 3.9s
    infinite;
  --animate-pulse-opacity: pulse-opacity 2s cubic-bezier(0.4, 0, 0.6, 1)
    infinite;
  --animate-lds-spinner: lds-spinner 1.2s linear infinite;

  /* Radix UI Animations */
  --animate-in: animate-in 0.2s ease-out;
  --animate-out: animate-out 0.2s ease-in;
  --animate-fade-in: fade-in 0.2s ease-out;
  --animate-fade-out: fade-out 0.2s ease-in;
  --animate-zoom-in: zoom-in 0.2s ease-out;
  --animate-zoom-out: zoom-out 0.2s ease-in;
  --animate-slide-in-from-top-2: slide-in-from-top-2 0.2s ease-out;
  --animate-slide-in-from-bottom-2: slide-in-from-bottom-2 0.2s ease-out;
  --animate-slide-in-from-left-2: slide-in-from-left-2 0.2s ease-out;
  --animate-slide-in-from-right-2: slide-in-from-right-2 0.2s ease-out;

  /* Primary */
  --color-primary-brand-02: #3300ff;

  --color-primary-black: #000000;
  --color-primary-white: #ffffff;

  /* NEW COLOR SYSTEM START */

  /* Main design colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-emerald-brand: #38cf86;
  --color-blue-brand: #3300ff;

  /* ESTO Emerald Scale */
  --color-emerald-50: #ecfdf3;
  --color-emerald-100: #d2f8e0;
  --color-emerald-200: #a9f1c7;
  --color-emerald-300: #71e4a8;
  --color-emerald-brand-tone: #38cf86;
  --color-emerald-500: #14b56d;
  --color-emerald-600: #099258;
  --color-emerald-700: #077549;
  --color-emerald-800: #085d3b;
  --color-emerald-900: #084c32;
  --color-emerald-950: #032b1c;

  /* ESTO Blue Scale */
  --color-blue-50: #f0f0ff;
  --color-blue-100: #e5e4ff;
  --color-blue-200: #cecdff;
  --color-blue-300: #aaa6ff;
  --color-blue-400: #8073ff;
  --color-blue-500: #593bff;
  --color-blue-600: #4514ff;
  --color-blue-brand-tone: #3300ff;
  --color-blue-800: #2c01d6;
  --color-blue-900: #2503af;
  --color-blue-950: #120077;

  /* Lime Scale */
  --color-lime-50: #f7fee7;
  --color-lime-100: #ecfccb;
  --color-lime-200: #d9f99d;
  --color-lime-300: #bef264;
  --color-lime-400: #a3e635;
  --color-lime-500: #84cc16;
  --color-lime-600: #65a30d;
  --color-lime-700: #4d7c0f;
  --color-lime-800: #3f6212;
  --color-lime-900: #365314;
  --color-lime-950: #1a2e05;

  /* Yellow Scale */
  --color-yellow-50: #fefce8;
  --color-yellow-100: #fef9c3;
  --color-yellow-200: #fef08a;
  --color-yellow-300: #fde047;
  --color-yellow-400: #facc15;
  --color-yellow-500: #eab308;
  --color-yellow-600: #ca8a04;
  --color-yellow-700: #a16207;
  --color-yellow-800: #854d0e;
  --color-yellow-900: #713f12;
  --color-yellow-950: #422006;

  /* Pink Scale */
  --color-pink-50: #fdf2f8;
  --color-pink-100: #fce7f3;
  --color-pink-200: #fbcfe8;
  --color-pink-300: #f9a8d4;
  --color-pink-400: #f472b6;
  --color-pink-500: #ec4899;
  --color-pink-600: #db2777;
  --color-pink-700: #be185d;
  --color-pink-800: #9d174d;
  --color-pink-900: #831843;
  --color-pink-950: #500724;

  /* Rose Scale */
  --color-rose-50: #fff1f2;
  --color-rose-100: #ffe4e6;
  --color-rose-200: #fecdd3;
  --color-rose-300: #fda4af;
  --color-rose-400: #fb7185;
  --color-rose-500: #f43f5e;
  --color-rose-600: #e11d48;
  --color-rose-700: #be123c;
  --color-rose-800: #9f1239;
  --color-rose-900: #881337;
  --color-rose-950: #4c0519;

  /* Purple Scale */
  --color-purple-50: #faf5ff;
  --color-purple-100: #f3e8ff;
  --color-purple-200: #e9d5ff;
  --color-purple-300: #d8b4fe;
  --color-purple-400: #c084fc;
  --color-purple-500: #a855f7;
  --color-purple-600: #9333ea;
  --color-purple-700: #7e22ce;
  --color-purple-800: #6b21a8;
  --color-purple-900: #581c87;
  --color-purple-950: #3b0764;

  /* Fuchsia Scale */
  --color-fuchsia-50: #fdf4ff;
  --color-fuchsia-100: #fae8ff;
  --color-fuchsia-200: #f5d0fe;
  --color-fuchsia-300: #f0abfc;
  --color-fuchsia-400: #e879f9;
  --color-fuchsia-500: #d946ef;
  --color-fuchsia-600: #c026d3;
  --color-fuchsia-700: #a21caf;
  --color-fuchsia-800: #86198f;
  --color-fuchsia-900: #701a75;
  --color-fuchsia-950: #4a044e;

  /* Indigo Scale */
  --color-indigo-50: #eef2ff;
  --color-indigo-100: #e0e7ff;
  --color-indigo-200: #c7d2fe;
  --color-indigo-300: #a5b4fc;
  --color-indigo-400: #818cf8;
  --color-indigo-500: #6366f1;
  --color-indigo-600: #4f46e5;
  --color-indigo-700: #4338ca;
  --color-indigo-800: #3730a3;
  --color-indigo-900: #312e81;
  --color-indigo-950: #1e1b4b;

  /* Violet Scale */
  --color-violet-50: #f5f3ff;
  --color-violet-100: #ede9fe;
  --color-violet-200: #ddd6fe;
  --color-violet-300: #c4b5fd;
  --color-violet-400: #a78bfa;
  --color-violet-500: #8b5cf6;
  --color-violet-600: #7c3aed;
  --color-violet-700: #6d28d9;
  --color-violet-800: #5b21b6;
  --color-violet-900: #4c1d95;
  --color-violet-950: #2e1065;

  /* Cyan Scale */
  --color-cyan-50: #ecfeff;
  --color-cyan-100: #cffafe;
  --color-cyan-200: #a5f3fc;
  --color-cyan-300: #67e8f9;
  --color-cyan-400: #22d3ee;
  --color-cyan-500: #06b6d4;
  --color-cyan-600: #0891b2;
  --color-cyan-700: #0e7490;
  --color-cyan-800: #155e75;
  --color-cyan-900: #164e63;
  --color-cyan-950: #083344;

  /* Sky Scale */
  --color-sky-50: #f0f9ff;
  --color-sky-100: #e0f2fe;
  --color-sky-200: #bae6fd;
  --color-sky-300: #7dd3fc;
  --color-sky-400: #38bdf8;
  --color-sky-500: #0ea5e9;
  --color-sky-600: #0284c7;
  --color-sky-700: #0369a1;
  --color-sky-800: #075985;
  --color-sky-900: #0c4a6e;
  --color-sky-950: #082f49;

  /* Green Scale */
  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-200: #bbf7d0;
  --color-green-300: #86efac;
  --color-green-400: #4ade80;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  --color-green-700: #15803d;
  --color-green-800: #166534;
  --color-green-900: #14532d;
  --color-green-950: #052e16;

  /* Teal Scale */
  --color-teal-50: #f0fdfa;
  --color-teal-100: #ccfbf1;
  --color-teal-200: #99f6e4;
  --color-teal-300: #5eead4;
  --color-teal-400: #2dd4bf;
  --color-teal-500: #14b8a6;
  --color-teal-600: #0d9488;
  --color-teal-700: #0f766e;
  --color-teal-800: #115e59;
  --color-teal-900: #134e4a;
  --color-teal-950: #042f2e;

  /* Orange Scale */
  --color-orange-50: #fff7ed;
  --color-orange-100: #ffedd5;
  --color-orange-200: #fed7aa;
  --color-orange-300: #fdba74;
  --color-orange-400: #fb923c;
  --color-orange-500: #f97316;
  --color-orange-600: #ea580c;
  --color-orange-700: #c2410c;
  --color-orange-800: #9a3412;
  --color-orange-900: #7c2d12;
  --color-orange-950: #431407;

  /* Amber Scale */
  --color-amber-50: #fffbeb;
  --color-amber-100: #fef3c7;
  --color-amber-200: #fde68a;
  --color-amber-300: #fcd34d;
  --color-amber-400: #fbbf24;
  --color-amber-500: #f59e0b;
  --color-amber-600: #d97706;
  --color-amber-700: #b45309;
  --color-amber-800: #92400e;
  --color-amber-900: #78350f;
  --color-amber-950: #451a03;

  /* Slate Scale */
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-200: #e2e8f0;
  --color-slate-300: #cbd5e1;
  --color-slate-400: #94a3b8;
  --color-slate-500: #64748b;
  --color-slate-600: #475569;
  --color-slate-700: #334155;
  --color-slate-800: #1e293b;
  --color-slate-900: #0f172a;
  --color-slate-950: #020617;

  /* Red Scale */
  --color-red-50: #fef2f2;
  --color-red-100: #fee2e2;
  --color-red-200: #fecaca;
  --color-red-300: #fca5a5;
  --color-red-400: #f87171;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;
  --color-red-700: #b91c1c;
  --color-red-800: #991b1b;
  --color-red-900: #7f1d1d;
  --color-red-950: #450a0a;

  /* Zinc Scale */
  --color-zinc-50: #fafafa;
  --color-zinc-100: #f4f4f5;
  --color-zinc-200: #e4e4e7;
  --color-zinc-300: #d4d4d8;
  --color-zinc-400: #a1a1aa;
  --color-zinc-500: #71717a;
  --color-zinc-600: #52525b;
  --color-zinc-700: #3f3f46;
  --color-zinc-800: #27272a;
  --color-zinc-900: #18181b;
  --color-zinc-950: #09090b;

  /* Neutral Scale */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0a0a0a;
  --color-neutral-200-2: #e5e5e5;

  /* Gray Scale */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  /* Stone Scale */
  --color-stone-50: #fafaf9;
  --color-stone-100: #f5f5f4;
  --color-stone-200: #e7e5e4;
  --color-stone-300: #d6d3d1;
  --color-stone-400: #a8a29e;
  --color-stone-500: #78716c;
  --color-stone-600: #57534e;
  --color-stone-700: #44403c;
  --color-stone-800: #292524;
  --color-stone-900: #1c1917;
  --color-stone-950: #0c0a09;

  /* Main text colors */
  --color-text-white: #ffffff;
  --color-text-black: #000000;
  --color-text-gray-600: #4b5563;
  --color-text-gray-400: #9ca3af;
  --color-text-gray-500: #6b7280;

  /* Main border colors */
  --color-border-gray-200: #e5e7eb;

  /* NEW COLOR SYSTEM END */

  /* System */
  --color-system-green: #38cf86;
  --color-system-green-800: #085d3b;
  --color-system-yellow: #ead40d;
  --color-system-yellow-400: #fbbf24;
  --color-system-orange: #ea770d;
  --color-system-red: #ea350d;
  /* Neutral */
  --color-neutral-900: #111827;
  --color-neutral-800: #1f2937;
  --color-neutral-700: #374151;
  --color-neutral-600: #4b5563;
  --color-neutral-500: #6b7280;
  --color-neutral-400: #9ca3af;
  --color-neutral-300: #d1d5db;
  --color-neutral-200: #e5e7eb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-50: #f9fafb;

  /* Typography Colors (using color-mix for alpha support) */
  --color-typography-black: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-neutral400: color-mix(
    in srgb,
    var(--color-neutral-400),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-neutral300: color-mix(
    in srgb,
    var(--color-neutral-300),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-white: color-mix(
    in srgb,
    var(--color-primary-white),
    transparent calc(100% - 100% * <alpha-value>)
  );

  /* ESTO Design System Typography Colors (using color-mix for alpha support) */
  --color-typography-emerald: color-mix(
    in srgb,
    var(--color-emerald-brand),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-emerald-600: color-mix(
    in srgb,
    var(--color-emerald-600),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-emerald-700: color-mix(
    in srgb,
    var(--color-emerald-700),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-blue-brand: color-mix(
    in srgb,
    var(--color-blue-brand),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-blue-600: color-mix(
    in srgb,
    var(--color-blue-600),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-blue-700: color-mix(
    in srgb,
    var(--color-blue-700),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-gray-600: color-mix(
    in srgb,
    var(--color-text-gray-600),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-gray-400: color-mix(
    in srgb,
    var(--color-text-gray-400),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-gray-500: color-mix(
    in srgb,
    var(--color-text-gray-500),
    transparent calc(100% - 100% * <alpha-value>)
  );

  /* Border Colors (using color-mix for alpha support) */
  --color-borders-neutral200: color-mix(
    in srgb,
    var(--color-neutral-200),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-borders-gray-200: color-mix(
    in srgb,
    var(--color-border-gray-200),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-borders-emerald: color-mix(
    in srgb,
    var(--color-emerald-brand),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-borders-emerald-200: color-mix(
    in srgb,
    var(--color-emerald-200),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-borders-emerald-300: color-mix(
    in srgb,
    var(--color-emerald-300),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-borders-blue-brand: color-mix(
    in srgb,
    var(--color-blue-brand),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-borders-blue-200: color-mix(
    in srgb,
    var(--color-blue-200),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-borders-blue-300: color-mix(
    in srgb,
    var(--color-blue-300),
    transparent calc(100% - 100% * <alpha-value>)
  );

  /* UI Component Colors (HSL-based, referencing :root variables) */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
}

/* Keyframes for animations */
@keyframes loader {
  to {
    opacity: 0.1;
    transform: translate3d(0, -0.25rem, 0);
  }
}

@keyframes lds-spinner {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes ride-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes ride-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade {
  0% {
    opacity: 1;
    height: 100%;
  }
  50% {
    opacity: 1;
    height: 100%;
  }
  100% {
    opacity: 0;
    height: 0;
  }
}

@keyframes indicator {
  0% {
    width: 0%;
    height: 0%;
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  100% {
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes pulse-and-pump {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: scale(1);
  }
  6.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: scale(0.95);
  }
  12.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: scale(0.9);
  }
  18.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: scale(0.85);
  }
  25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: scale(0.8);
  }
  31.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 0%);
    transform: scale(0.75);
  }
  37.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: scale(0.8);
  }
  43.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: scale(0.85);
  }
  50% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: scale(0.9);
  }
  56.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: scale(0.95);
  }
  62.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: scale(1);
  }
  68.75%,
  75%,
  81.25%,
  87.5%,
  93.75%,
  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

@keyframes pulse-opacity {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes pulse {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
  6.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }
  12.5% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }
  18.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }
  25% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }
  31.25% {
    background: radial-gradient(circle at center, #e2e2e2 45%, #fcfcfc 66%);
  }
  37.5% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }
  43.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }
  50% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }
  56.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }
  62.5% {
    background: radial-gradient(circle at center, #e2e2e2 #fcfcfc 66%);
  }
  68.75%,
  75%,
  81.25%,
  87.5%,
  93.75%,
  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

@keyframes slow-fade-pulse {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #ffffff 70%);
  }
  3.125% {
    background: radial-gradient(circle at center, #e2e2e2 10%, #ffffff 70%);
  }
  6.25% {
    background: radial-gradient(circle at center, #e2e2e2 20%, #ffffff 70%);
  }
  9.375% {
    background: radial-gradient(circle at center, #e2e2e2 30%, #ffffff 70%);
  }
  12.5% {
    background: radial-gradient(circle at center, #e2e2e2 40%, #ffffff 70%);
  }
  15.625%,
  21.875% {
    background: radial-gradient(circle at center, #e2e2e2 40%, #ffffff 70%);
  }
  25% {
    background: radial-gradient(circle at center, #e2e2e2 38%, #ffffff 70%);
  }
  31.25% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #ffffff 70%);
  }
  37.5% {
    background: radial-gradient(circle at center, #e2e2e2 34%, #ffffff 70%);
  }
  43.75% {
    background: radial-gradient(circle at center, #e2e2e2 32%, #ffffff 70%);
  }
  46.875% {
    background: radial-gradient(circle at center, #e2e2e2 30%, #ffffff 70%);
  }
  50% {
    background: radial-gradient(circle at center, #e2e2e2 28%, #ffffff 70%);
  }
  53.125% {
    background: radial-gradient(circle at center, #e2e2e2 26%, #ffffff 70%);
  }
  62.5% {
    background: radial-gradient(circle at center, #e2e2e2 24%, #ffffff 70%);
  }
  65.625% {
    background: radial-gradient(circle at center, #e2e2e2 22%, #ffffff 70%);
  }
  68.75% {
    background: radial-gradient(circle at center, #e2e2e2 20%, #ffffff 70%);
  }
  71.875% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #ffffff 70%);
  }
  75% {
    background: radial-gradient(circle at center, #e2e2e2 16%, #ffffff 70%);
  }
  100% {
    background: radial-gradient(circle at center, #e2e2e2 0%, #ffffff 70%);
  }
}

@keyframes rotate-user {
  0% {
    transform: rotate(-90deg);
  }
  12.39% {
    transform: rotate(-90deg);
  }
  16.66% {
    transform: rotate(30deg);
  }
  45.71% {
    transform: rotate(30deg);
  }
  49.98% {
    transform: rotate(180deg);
  }
  79.48% {
    transform: rotate(180deg);
  }
  83.75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(270deg);
  }
}

@keyframes rotate-reverse-user {
  0% {
    transform: rotate(90deg);
  }
  12.39% {
    transform: rotate(90deg);
  }
  16.66% {
    transform: rotate(-30deg);
  }
  45.71% {
    transform: rotate(-30deg);
  }
  49.98% {
    transform: rotate(-180deg);
  }
  79.48% {
    transform: rotate(-180deg);
  }
  83.75% {
    transform: rotate(-270deg);
  }
  100% {
    transform: rotate(-270deg);
  }
}

@keyframes rotate-euro {
  0% {
    transform: rotate(90deg);
  }
  29.05% {
    transform: rotate(90deg);
  }
  33.32% {
    transform: rotate(180deg);
  }
  45.57% {
    transform: rotate(180deg);
  }
  49.98% {
    transform: rotate(245deg);
  }
  62.37% {
    transform: rotate(245deg);
  }
  79.03% {
    transform: rotate(245deg);
  }
  83.3% {
    transform: rotate(300deg);
  }
  95.69% {
    transform: rotate(300deg);
  }
  100% {
    transform: rotate(450deg);
  }
}

@keyframes rotate-reverse-euro {
  0% {
    transform: rotate(-90deg);
  }
  29.05% {
    transform: rotate(-90deg);
  }
  33.32% {
    transform: rotate(-180deg);
  }
  45.57% {
    transform: rotate(-180deg);
  }
  49.98% {
    transform: rotate(-245deg);
  }
  62.37% {
    transform: rotate(-245deg);
  }
  79.03% {
    transform: rotate(-245deg);
  }
  83.3% {
    transform: rotate(-300deg);
  }
  95.69% {
    transform: rotate(-300deg);
  }
  100% {
    transform: rotate(-450deg);
  }
}

@keyframes rotate-bank {
  0% {
    transform: rotate(0deg);
  }
  45.71% {
    transform: rotate(0deg);
  }
  49.98% {
    transform: rotate(45deg);
  }
  62.37% {
    transform: rotate(45deg);
  }
  66.64% {
    transform: rotate(170deg);
  }
  79.03% {
    transform: rotate(170deg);
  }
  83.3% {
    transform: rotate(245deg);
  }
  95.69% {
    transform: rotate(245deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotate-reverse-bank {
  0% {
    transform: rotate(0deg);
  }
  45.71% {
    transform: rotate(0deg);
  }
  49.98% {
    transform: rotate(-45deg);
  }
  62.37% {
    transform: rotate(-45deg);
  }
  66.64% {
    transform: rotate(-170deg);
  }
  79.03% {
    transform: rotate(-170deg);
  }
  83.3% {
    transform: rotate(-245deg);
  }
  95.69% {
    transform: rotate(-245deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

/* Radix UI Animation Keyframes */
@keyframes animate-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes animate-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoom-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes slide-in-from-top-2 {
  from {
    transform: translateY(-0.5rem);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom-2 {
  from {
    transform: translateY(0.5rem);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-top {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-left-2 {
  from {
    transform: translateX(-0.5rem);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slide-in-from-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-right-2 {
  from {
    transform: translateX(0.5rem);
  }
  to {
    transform: translateX(0);
  }
}

@layer base {
  /* CSS Reset - moved here to ensure proper cascade order with Tailwind utilities */
  html,
  body,
  div,
  span,
  applet,
  object,
  iframe,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  blockquote,
  pre,
  a,
  abbr,
  acronym,
  address,
  big,
  cite,
  code,
  del,
  dfn,
  em,
  img,
  ins,
  kbd,
  q,
  s,
  samp,
  small,
  strike,
  strong,
  sub,
  sup,
  tt,
  var,
  b,
  u,
  i,
  center,
  dl,
  dt,
  dd,
  ol,
  ul,
  li,
  fieldset,
  option,
  form,
  label,
  legend,
  table,
  caption,
  tbody,
  tfoot,
  thead,
  tr,
  th,
  td,
  article,
  aside,
  canvas,
  details,
  embed,
  figure,
  figcaption,
  footer,
  header,
  hgroup,
  menu,
  nav,
  output,
  ruby,
  section,
  summary,
  time,
  mark,
  audio,
  input,
  video {
    -webkit-tap-highlight-color: transparent;
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    /* stylelint-disable-next-line declaration-block-no-shorthand-property-overrides */
    font: inherit;
    vertical-align: baseline;
  }

  /* HTML5 display-role reset for older browsers */
  article,
  aside,
  details,
  figcaption,
  figure,
  footer,
  header,
  hgroup,
  menu,
  nav,
  section {
    display: block;
  }

  body {
    line-height: 1;
  }

  ol,
  ul {
    list-style: none;
  }

  blockquote,
  q {
    quotes: none;
  }

  blockquote::before,
  blockquote::after,
  q::before,
  q::after {
    content: '';
    content: none;
  }

  table {
    border-collapse: collapse;
    border-spacing: 0;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
  }

  /* This is needed to fix the z-index of the popover */
  [data-radix-popper-content-wrapper] {
    z-index: 300 !important;
  }
}

@layer base {
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  body:has(.app-modal) {
    overflow: hidden;
  }
}

/* Our configs */
/* Color design system */
/* Link => https://www.figma.com/file/qRNfEkalc67TUhyFm6AjFf/ESTO-Design-System?type=design&node-id=1-6481&mode=design&t=cv07M8IvBhRx2JAL-0 */

@layer base {
  html,
  body,
  #root {
    /* font-family-inter */
    height: 100%;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 0.2s;
  }
  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .underline-from-font {
    text-underline-position: from-font;
  }
}
