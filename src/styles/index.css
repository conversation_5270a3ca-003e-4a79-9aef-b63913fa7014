*,
::before,
::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  /* 3 */
  tab-size: 4;
  /* 3 */
  font-family:
    ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol', 'Noto Color Emoji';
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role='button'] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden='until-found'])) {
  display: none;
}

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;
}

/* This is needed to fix the z-index of the popover */

[data-radix-popper-content-wrapper] {
  z-index: 300 !important;
}

* {
  /* @apply border-border; */
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

html,
body,
#root {
  /* font-inter */
  height: 100%;
}

:root {
  /* Primary */
  --color-primary-brand-02: #3300ff;
  --color-primary-black: #000000;
  --color-primary-white: #ffffff;
  /* System */
  --color-system-green: #38cf86;
  --color-system-yellow: #ead40d;
  --color-system-orange: #ea770d;
  --color-system-red: #ea350d;
  /* Neutral */
  --color-neutral-900: #111827;
  --color-neutral-800: #1f2937;
  --color-neutral-700: #374151;
  --color-neutral-600: #4b5563;
  --color-neutral-500: #6b7280;
  --color-neutral-400: #9ca3af;
  --color-neutral-300: #d1d5db;
  --color-neutral-200: #e5e7eb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-50: #f9fafb;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}

@media (min-width: 390px) {
  .container {
    max-width: 390px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1440px) {
  .container {
    max-width: 1440px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.\!pointer-events-none {
  pointer-events: none !important;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.\!visible {
  visibility: visible !important;
}

.visible {
  visibility: visible;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.\!left-\[0\.3rem\] {
  left: 0.3rem !important;
}

.-bottom-1 {
  bottom: -0.25rem;
}

.-bottom-12 {
  bottom: -3rem;
}

.-left-12 {
  left: -3rem;
}

.-right-12 {
  right: -3rem;
}

.-top-12 {
  top: -3rem;
}

.bottom-0 {
  bottom: 0px;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-2 {
  left: 0.5rem;
}

.left-9 {
  left: 2.25rem;
}

.left-\[\.625rem\] {
  left: 0.625rem;
}

.left-\[50\%\] {
  left: 50%;
}

.left-\[80\%\] {
  left: 80%;
}

.right-2 {
  right: 0.5rem;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: 0.5rem;
}

.top-6 {
  top: 1.5rem;
}

.top-\[50\%\] {
  top: 50%;
}

.top-\[9\.5rem\] {
  top: 9.5rem;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-50 {
  z-index: 50;
}

.z-\[200\] {
  z-index: 200;
}

.z-\[20\] {
  z-index: 20;
}

.\!my-8 {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-\[0\.25rem\] {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.\!-mt-2 {
  margin-top: -0.5rem !important;
}

.\!mb-0 {
  margin-bottom: 0px !important;
}

.\!mt-0 {
  margin-top: 0px !important;
}

.\!mt-1\.5 {
  margin-top: 0.375rem !important;
}

.\!mt-10 {
  margin-top: 2.5rem !important;
}

.\!mt-4 {
  margin-top: 1rem !important;
}

.\!mt-6 {
  margin-top: 1.5rem !important;
}

.\!mt-8 {
  margin-top: 2rem !important;
}

.\!mt-\[1\.25rem\] {
  margin-top: 1.25rem !important;
}

.-ml-4 {
  margin-left: -1rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-2\.5 {
  margin-bottom: 0.625rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-\[5\.25rem\] {
  margin-bottom: 5.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-2\.5 {
  margin-left: 0.625rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-\[0\.125rem\] {
  margin-left: 0.125rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-\[0\.0875rem\] {
  margin-right: 0.0875rem;
}

.mt-0 {
  margin-top: 0px;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-2\.5 {
  margin-top: 0.625rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-7 {
  margin-top: 1.75rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-\[0\.125rem\] {
  margin-top: 0.125rem;
}

.mt-\[1\.125rem\] {
  margin-top: 1.125rem;
}

.mt-auto {
  margin-top: auto;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.\!inline {
  display: inline !important;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.size-10 {
  width: 2.5rem;
  height: 2.5rem;
}

.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}

.size-2\.5 {
  width: 0.625rem;
  height: 0.625rem;
}

.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}

.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}

.size-8 {
  width: 2rem;
  height: 2rem;
}

.size-\[1\.125rem\] {
  width: 1.125rem;
  height: 1.125rem;
}

.size-full {
  width: 100%;
  height: 100%;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-2 {
  height: 0.5rem;
}

.h-20 {
  height: 5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-3\.5 {
  height: 0.875rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-\[0\.3rem\] {
  height: 0.3rem;
}

.h-\[0\.750rem\] {
  height: 0.75rem;
}

.h-\[130px\] {
  height: 130px;
}

.h-\[16\.75rem\] {
  height: 16.75rem;
}

.h-\[176px\] {
  height: 176px;
}

.h-\[19rem\] {
  height: 19rem;
}

.h-\[1px\] {
  height: 1px;
}

.h-\[20rem\] {
  height: 20rem;
}

.h-\[210px\] {
  height: 210px;
}

.h-\[23\.125rem\] {
  height: 23.125rem;
}

.h-\[235px\] {
  height: 235px;
}

.h-\[27rem\] {
  height: 27rem;
}

.h-\[290px\] {
  height: 290px;
}

.h-\[3\.5rem\] {
  height: 3.5rem;
}

.h-\[370px\] {
  height: 370px;
}

.h-\[4rem\] {
  height: 4rem;
}

.h-\[50px\] {
  height: 50px;
}

.h-\[5rem\] {
  height: 5rem;
}

.h-\[9\.4375rem\] {
  height: 9.4375rem;
}

.h-\[calc\(100\%-5rem\)\] {
  height: calc(100% - 5rem);
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.max-h-0 {
  max-height: 0px;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[100px\] {
  max-height: 100px;
}

.max-h-\[500px\] {
  max-height: 500px;
}

.min-h-\[0\.3rem\] {
  min-height: 0.3rem;
}

.min-h-\[6\.25rem\] {
  min-height: 6.25rem;
}

.min-h-fit {
  min-height: fit-content;
}

.min-h-screen {
  min-height: 100vh;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-2 {
  width: 0.5rem;
}

.w-20 {
  width: 5rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\.5 {
  width: 0.875rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-7 {
  width: 1.75rem;
}

.w-72 {
  width: 18rem;
}

.w-8 {
  width: 2rem;
}

.w-\[0\.3rem\] {
  width: 0.3rem;
}

.w-\[130px\] {
  width: 130px;
}

.w-\[176px\] {
  width: 176px;
}

.w-\[1px\] {
  width: 1px;
}

.w-\[210px\] {
  width: 210px;
}

.w-\[23\.125rem\] {
  width: 23.125rem;
}

.w-\[235px\] {
  width: 235px;
}

.w-\[290px\] {
  width: 290px;
}

.w-\[3\.5rem\] {
  width: 3.5rem;
}

.w-\[370px\] {
  width: 370px;
}

.w-\[5\.4375rem\] {
  width: 5.4375rem;
}

.w-\[7\.6125rem\] {
  width: 7.6125rem;
}

.w-\[7\.628rem\] {
  width: 7.628rem;
}

.w-\[7rem\] {
  width: 7rem;
}

.w-\[87px\] {
  width: 87px;
}

.w-\[calc\(100\%-1rem\)\] {
  width: calc(100% - 1rem);
}

.w-\[calc\(100\%-2\.35rem\)\] {
  width: calc(100% - 2.35rem);
}

.w-\[calc\(100\%-4\.1rem\)\] {
  width: calc(100% - 4.1rem);
}

.w-\[var\(--radix-select-trigger-width\)\] {
  width: var(--radix-select-trigger-width);
}

.w-fit {
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-screen {
  width: 100vw;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-\[0\.3rem\] {
  min-width: 0.3rem;
}

.min-w-\[7\.5rem\] {
  min-width: 7.5rem;
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-72 {
  max-width: 18rem;
}

.max-w-\[10\.2rem\] {
  max-width: 10.2rem;
}

.max-w-\[18rem\] {
  max-width: 18rem;
}

.max-w-\[25rem\] {
  max-width: 25rem;
}

.max-w-\[90rem\] {
  max-width: 90rem;
}

.max-w-md {
  max-width: 28rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.basis-full {
  flex-basis: 100%;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\.5 {
  --tw-translate-y: -0.375rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-2 {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[\.1rem\] {
  --tw-translate-y: -0.1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-2 {
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-45 {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[70\%\] {
  --tw-scale-x: 70%;
  --tw-scale-y: 70%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes lds-spinner {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.animate-lds-spinner {
  animation: lds-spinner 1.2s linear infinite;
}

@keyframes loader {
  to {
    opacity: 0.1;
    transform: translate3d(0, -0.25rem, 0);
  }
}

.animate-loader {
  animation: loader 0.6s infinite alternate;
}

@keyframes pulse {
  50% {
    opacity: 0.5;
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }

  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  6.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }

  12.5% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }

  18.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }

  25% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }

  31.25% {
    background: radial-gradient(circle at center, #e2e2e2 45%, #fcfcfc 66%);
  }

  37.5% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }

  43.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }

  56.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }

  62.5% {
    background: radial-gradient(circle at center, #e2e2e2 #fcfcfc 66%);
  }

  68.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  81.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  87.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  93.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

.animate-pulse {
  animation: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.4s infinite;
}

@keyframes pulse {
  50% {
    opacity: 0.5;
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }

  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  6.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }

  12.5% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }

  18.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }

  25% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }

  31.25% {
    background: radial-gradient(circle at center, #e2e2e2 45%, #fcfcfc 66%);
  }

  37.5% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }

  43.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }

  56.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }

  62.5% {
    background: radial-gradient(circle at center, #e2e2e2 #fcfcfc 66%);
  }

  68.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  81.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  87.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  93.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

.animate-pulse-2 {
  animation: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.7s infinite;
}

@keyframes slow-fade-pulse {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #ffffff 70%);
  }

  3.125% {
    background: radial-gradient(circle at center, #e2e2e2 10%, #ffffff 70%);
  }

  6.25% {
    background: radial-gradient(circle at center, #e2e2e2 20%, #ffffff 70%);
  }

  9.375% {
    background: radial-gradient(circle at center, #e2e2e2 30%, #ffffff 70%);
  }

  12.5% {
    background: radial-gradient(circle at center, #e2e2e2 40%, #ffffff 70%);
  }

  15.625% {
    background: radial-gradient(circle at center, #e2e2e2 40%, #ffffff 70%);
  }

  21.875% {
    background: radial-gradient(circle at center, #e2e2e2 40%, #ffffff 70%);
  }

  25% {
    background: radial-gradient(circle at center, #e2e2e2 38%, #ffffff 70%);
  }

  31.25% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #ffffff 70%);
  }

  37.5% {
    background: radial-gradient(circle at center, #e2e2e2 34%, #ffffff 70%);
  }

  43.75% {
    background: radial-gradient(circle at center, #e2e2e2 32%, #ffffff 70%);
  }

  46.875% {
    background: radial-gradient(circle at center, #e2e2e2 30%, #ffffff 70%);
  }

  50% {
    background: radial-gradient(circle at center, #e2e2e2 28%, #ffffff 70%);
  }

  53.125% {
    background: radial-gradient(circle at center, #e2e2e2 26%, #ffffff 70%);
  }

  62.5% {
    background: radial-gradient(circle at center, #e2e2e2 24%, #ffffff 70%);
  }

  65.625% {
    background: radial-gradient(circle at center, #e2e2e2 22%, #ffffff 70%);
  }

  68.75% {
    background: radial-gradient(circle at center, #e2e2e2 20%, #ffffff 70%);
  }

  71.875% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #ffffff 70%);
  }

  75% {
    background: radial-gradient(circle at center, #e2e2e2 16%, #ffffff 70%);
  }

  100% {
    background: radial-gradient(circle at center, #e2e2e2 0%, #ffffff 70%);
  }
}

.animate-pulse-3 {
  animation: slow-fade-pulse 2s cubic-bezier(0, 0.85, 0, 0.75) 0.9s infinite;
}

@keyframes pulse-and-pump {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: translate(-50%, -50%) scale(1);
  }

  6.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: translate(-50%, -50%) scale(0.95);
  }

  12.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: translate(-50%, -50%) scale(0.9);
  }

  18.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: translate(-50%, -50%) scale(0.85);
  }

  25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: translate(-50%, -50%) scale(0.8);
  }

  31.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 0%);
    transform: translate(-50%, -50%) scale(0.75);
  }

  37.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: translate(-50%, -50%) scale(0.8);
  }

  43.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: translate(-50%, -50%) scale(0.85);
  }

  50% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: translate(-50%, -50%) scale(0.9);
  }

  56.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: translate(-50%, -50%) scale(0.95);
  }

  62.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: translate(-50%, -50%) scale(1);
  }

  68.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  81.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  87.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  93.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }

  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

.animate-pulse-and-pump {
  animation: pulse-and-pump 0.5s cubic-bezier(0.21, 0.35, 0.44, 0.99) infinite;
}

@keyframes pulse-opacity {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-pulse-opacity {
  animation: pulse-opacity 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes rotate-bank {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  45.71% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  49.98% {
    transform: translate(-50%, -50%) rotate(45deg);
  }

  62.37% {
    transform: translate(-50%, -50%) rotate(45deg);
  }

  66.64% {
    transform: translate(-50%, -50%) rotate(170deg);
  }

  79.03% {
    transform: translate(-50%, -50%) rotate(170deg);
  }

  83.3% {
    transform: translate(-50%, -50%) rotate(245deg);
  }

  95.69% {
    transform: translate(-50%, -50%) rotate(245deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.animate-rotate-bank {
  animation: rotate-bank 23.4s ease-in-out 3.9s infinite;
}

@keyframes rotate-euro {
  0% {
    transform: translate(-50%, -50%) rotate(90deg);
  }

  29.05% {
    transform: translate(-50%, -50%) rotate(90deg);
  }

  33.32% {
    transform: translate(-50%, -50%) rotate(180deg);
  }

  45.57% {
    transform: translate(-50%, -50%) rotate(180deg);
  }

  49.98% {
    transform: translate(-50%, -50%) rotate(245deg);
  }

  62.37% {
    transform: translate(-50%, -50%) rotate(245deg);
  }

  79.03% {
    transform: translate(-50%, -50%) rotate(245deg);
  }

  83.3% {
    transform: translate(-50%, -50%) rotate(300deg);
  }

  95.69% {
    transform: translate(-50%, -50%) rotate(300deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(450deg);
  }
}

.animate-rotate-euro {
  animation: rotate-euro 23.4s ease-in-out 3.9s infinite;
}

@keyframes rotate-reverse-bank {
  0% {
    transform: rotate(0deg);
  }

  45.71% {
    transform: rotate(0deg);
  }

  49.98% {
    transform: rotate(-45deg);
  }

  62.37% {
    transform: rotate(-45deg);
  }

  66.64% {
    transform: rotate(-170deg);
  }

  79.03% {
    transform: rotate(-170deg);
  }

  83.3% {
    transform: rotate(-245deg);
  }

  95.69% {
    transform: rotate(-245deg);
  }

  100% {
    transform: rotate(-360deg);
  }
}

.animate-rotate-reverse-bank {
  animation: rotate-reverse-bank 23.4s ease-in-out 3.9s infinite;
}

@keyframes rotate-reverse-euro {
  0% {
    transform: rotate(-90deg);
  }

  29.05% {
    transform: rotate(-90deg);
  }

  33.32% {
    transform: rotate(-180deg);
  }

  45.57% {
    transform: rotate(-180deg);
  }

  49.98% {
    transform: rotate(-245deg);
  }

  62.37% {
    transform: rotate(-245deg);
  }

  79.03% {
    transform: rotate(-245deg);
  }

  83.3% {
    transform: rotate(-300deg);
  }

  95.69% {
    transform: rotate(-300deg);
  }

  100% {
    transform: rotate(-450deg);
  }
}

.animate-rotate-reverse-euro {
  animation: rotate-reverse-euro 23.4s ease-in-out 3.9s infinite;
}

@keyframes rotate-reverse-user {
  0% {
    transform: rotate(90deg);
  }

  12.39% {
    transform: rotate(90deg);
  }

  16.66% {
    transform: rotate(-30deg);
  }

  45.71% {
    transform: rotate(-30deg);
  }

  49.98% {
    transform: rotate(-180deg);
  }

  79.48% {
    transform: rotate(-180deg);
  }

  83.75% {
    transform: rotate(-270deg);
  }

  100% {
    transform: rotate(-270deg);
  }
}

.animate-rotate-reverse-user {
  animation: rotate-reverse-user 23.4s ease-in-out 3.9s infinite;
}

@keyframes rotate-user {
  0% {
    transform: translate(-50%, -50%) rotate(-90deg);
  }

  12.39% {
    transform: translate(-50%, -50%) rotate(-90deg);
  }

  16.66% {
    transform: translate(-50%, -50%) rotate(30deg);
  }

  45.71% {
    transform: translate(-50%, -50%) rotate(30deg);
  }

  49.98% {
    transform: translate(-50%, -50%) rotate(180deg);
  }

  79.48% {
    transform: translate(-50%, -50%) rotate(180deg);
  }

  83.75% {
    transform: translate(-50%, -50%) rotate(270deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(270deg);
  }
}

.animate-rotate-user {
  animation: rotate-user 23.4s ease-in-out 3.9s infinite;
}

.cursor-default {
  cursor: default;
}

.cursor-grab {
  cursor: grab;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-progress {
  cursor: progress;
}

.touch-none {
  touch-action: none;
}

.select-none {
  -webkit-user-select: none;
  user-select: none;
}

.resize {
  resize: both;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-\[1fr_auto\] {
  grid-template-columns: 1fr auto;
}

.grid-cols-\[auto\2c 1fr\] {
  grid-template-columns: auto 1fr;
}

.grid-cols-\[repeat\(auto-fill\2c minmax\(8rem\2c 1fr\)\)\] {
  grid-template-columns: repeat(auto-fill, minmax(8rem, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.place-items-center {
  place-items: center;
}

.content-center {
  align-content: center;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.justify-normal {
  justify-content: normal;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-items-center {
  justify-items: center;
}

.\!gap-1 {
  gap: 0.25rem !important;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-\[0\.125rem\] {
  gap: 0.125rem;
}

.gap-\[0\.1875rem\] {
  gap: 0.1875rem;
}

.gap-\[0\.3125rem\] {
  gap: 0.3125rem;
}

.gap-\[1\.5rem\] {
  gap: 1.5rem;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[1rem\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-balance {
  text-wrap: balance;
}

.break-words {
  overflow-wrap: break-word;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-\[\.375rem\] {
  border-radius: 0.375rem;
}

.rounded-\[\.625rem\] {
  border-radius: 0.625rem;
}

.rounded-\[\.875rem\] {
  border-radius: 0.875rem;
}

.rounded-\[0\.875rem\] {
  border-radius: 0.875rem;
}

.rounded-\[14px\] {
  border-radius: 14px;
}

.rounded-\[20\%\] {
  border-radius: 20%;
}

.rounded-\[6\.25rem\] {
  border-radius: 6.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-xxl {
  border-radius: 1.5rem;
}

.rounded-b-none {
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}

.rounded-l-full {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.rounded-t-none {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-\[0\.0625rem\] {
  border-width: 0.0625rem;
}

.border-\[0\.25rem\] {
  border-width: 0.25rem;
}

.border-b {
  border-bottom-width: 1px;
}

.border-t-\[2px\] {
  border-top-width: 2px;
}

.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.\!border-primary-black {
  --tw-border-opacity: 1 !important;
  border-color: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  ) !important;
}

.\!border-system-red {
  --tw-border-opacity: 1 !important;
  border-color: color-mix(
    in srgb,
    var(--color-system-red),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  ) !important;
}

.border-\[\#FEDBA3\] {
  --tw-border-opacity: 1;
  border-color: rgb(254 219 163 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-neutral-100 {
  border-color: var(--color-neutral-100);
}

.border-neutral-200 {
  border-color: var(--color-neutral-200);
}

.border-neutral-400 {
  border-color: var(--color-neutral-400);
}

.border-neutral-50 {
  border-color: var(--color-neutral-50);
}

.border-primary {
  border-color: hsl(var(--primary));
}

.border-primary-black {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-system-red {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-system-red),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.bg-\[\#F8F5E1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(248 245 225 / var(--tw-bg-opacity, 1));
}

.bg-\[\#fbf9ec\] {
  --tw-bg-opacity: 1;
  background-color: rgb(251 249 236 / var(--tw-bg-opacity, 1));
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-border {
  background-color: hsl(var(--border));
}

.bg-card {
  background-color: hsl(var(--card));
}

.bg-muted {
  background-color: hsl(var(--muted));
}

.bg-neutral-100 {
  background-color: var(--color-neutral-100);
}

.bg-neutral-200 {
  background-color: var(--color-neutral-200);
}

.bg-neutral-400 {
  background-color: var(--color-neutral-400);
}

.bg-neutral-50 {
  background-color: var(--color-neutral-50);
}

.bg-popover {
  background-color: hsl(var(--popover));
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.bg-primary-black {
  --tw-bg-opacity: 1;
  background-color: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * var(--tw-bg-opacity, 1))
  );
}

.bg-primary-brand02 {
  --tw-bg-opacity: 1;
  background-color: color-mix(
    in srgb,
    var(--color-primary-brand-02),
    transparent calc(100% - 100% * var(--tw-bg-opacity, 1))
  );
}

.bg-primary-white {
  --tw-bg-opacity: 1;
  background-color: color-mix(
    in srgb,
    var(--color-primary-white),
    transparent calc(100% - 100% * var(--tw-bg-opacity, 1))
  );
}

.bg-system-green {
  --tw-bg-opacity: 1;
  background-color: color-mix(
    in srgb,
    var(--color-system-green),
    transparent calc(100% - 100% * var(--tw-bg-opacity, 1))
  );
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-\[radial-gradient\(circle_at_center\2c \#E2E2E2\2c \#FCFCFC_66\%\)\] {
  background-image: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
}

.bg-\[radial-gradient\(circle_at_center\2c \#E2E2E2\2c \#FFFFFF_70\%\)\] {
  background-image: radial-gradient(circle at center, #e2e2e2, #ffffff 70%);
}

.bg-cover {
  background-size: cover;
}

.bg-\[0\%_30\%\] {
  background-position: 0% 30%;
}

.bg-center {
  background-position: center;
}

.fill-current {
  fill: currentColor;
}

.fill-primary {
  fill: hsl(var(--primary));
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-\[0\.875rem\] {
  padding: 0.875rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-\[1\.5rem\] {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-\[1\.75rem\] {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-\[18px\] {
  padding-left: 18px;
  padding-right: 18px;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-3\.5 {
  padding-left: 0.875rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pl-9 {
  padding-left: 2.25rem;
}

.pl-\[0\.875rem\] {
  padding-left: 0.875rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-2\.5 {
  padding-right: 0.625rem;
}

.pr-3\.5 {
  padding-right: 0.875rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pr-9 {
  padding-right: 2.25rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-start {
  text-align: start;
}

.font-inter {
  font-family: Inter, sans-serif;
}

.\!text-\[0\.625rem\] {
  font-size: 0.625rem !important;
}

.text-\[\.750rem\] {
  font-size: 0.75rem;
}

.text-\[\.875rem\] {
  font-size: 0.875rem;
}

.text-\[0\.875rem\] {
  font-size: 0.875rem;
}

.text-\[1\.125rem\] {
  font-size: 1.125rem;
}

.text-\[1\.25rem\] {
  font-size: 1.25rem;
}

.text-\[1\.5rem\] {
  font-size: 1.5rem;
}

.text-\[1\.75rem\] {
  font-size: 1.75rem;
}

.text-\[1rem\] {
  font-size: 1rem;
}

.text-\[2\.25rem\] {
  font-size: 2.25rem;
}

.text-\[2rem\] {
  font-size: 2rem;
}

.text-\[3rem\] {
  font-size: 3rem;
}

.text-\[4rem\] {
  font-size: 4rem;
}

.text-\[5\.25rem\] {
  font-size: 5.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.italic {
  font-style: italic;
}

.\!leading-\[1\.125rem\] {
  line-height: 1.125rem !important;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-\[1\.25rem\] {
  line-height: 1.25rem;
}

.leading-\[1\.5rem\] {
  line-height: 1.5rem;
}

.leading-\[1\.75rem\] {
  line-height: 1.75rem;
}

.leading-\[2\.25rem\] {
  line-height: 2.25rem;
}

.leading-\[2\.5rem\] {
  line-height: 2.5rem;
}

.leading-\[2rem\] {
  line-height: 2rem;
}

.leading-\[3rem\] {
  line-height: 3rem;
}

.leading-\[4rem\] {
  line-height: 4rem;
}

.leading-\[5\.25rem\] {
  line-height: 5.25rem;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.tracking-\[-\.00875rem\] {
  letter-spacing: -0.00875rem;
}

.tracking-\[-\.02rem\] {
  letter-spacing: -0.02rem;
}

.tracking-\[-\.03375rem\] {
  letter-spacing: -0.03375rem;
}

.tracking-\[-\.045rem\] {
  letter-spacing: -0.045rem;
}

.tracking-\[-\.05rem\] {
  letter-spacing: -0.05rem;
}

.tracking-\[-\.09rem\] {
  letter-spacing: -0.09rem;
}

.tracking-\[-\.105rem\] {
  letter-spacing: -0.105rem;
}

.tracking-\[-\.12rem\] {
  letter-spacing: -0.12rem;
}

.tracking-\[-\.135rem\] {
  letter-spacing: -0.135rem;
}

.tracking-\[-\.18rem\] {
  letter-spacing: -0.18rem;
}

.tracking-\[-\.24rem\] {
  letter-spacing: -0.24rem;
}

.tracking-\[-\.36rem\] {
  letter-spacing: -0.36rem;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.\!text-neutral-500 {
  color: var(--color-neutral-500) !important;
}

.text-\[\#ffce84\] {
  --tw-text-opacity: 1;
  color: rgb(255 206 132 / var(--tw-text-opacity, 1));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-card-foreground {
  color: hsl(var(--card-foreground));
}

.text-current {
  color: currentColor;
}

.text-inherit {
  color: inherit;
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-neutral-200 {
  color: var(--color-neutral-200);
}

.text-neutral-300 {
  color: var(--color-neutral-300);
}

.text-neutral-400 {
  color: var(--color-neutral-400);
}

.text-neutral-500 {
  color: var(--color-neutral-500);
}

.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}

.text-primary {
  color: hsl(var(--primary));
}

.text-primary-black {
  --tw-text-opacity: 1;
  color: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * var(--tw-text-opacity, 1))
  );
}

.text-primary-brand02 {
  --tw-text-opacity: 1;
  color: color-mix(
    in srgb,
    var(--color-primary-brand-02),
    transparent calc(100% - 100% * var(--tw-text-opacity, 1))
  );
}

.text-primary-white {
  --tw-text-opacity: 1;
  color: color-mix(
    in srgb,
    var(--color-primary-white),
    transparent calc(100% - 100% * var(--tw-text-opacity, 1))
  );
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-system-green {
  --tw-text-opacity: 1;
  color: color-mix(
    in srgb,
    var(--color-system-green),
    transparent calc(100% - 100% * var(--tw-text-opacity, 1))
  );
}

.text-system-red {
  --tw-text-opacity: 1;
  color: color-mix(
    in srgb,
    var(--color-system-red),
    transparent calc(100% - 100% * var(--tw-text-opacity, 1))
  );
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.no-underline {
  text-decoration-line: none;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-80 {
  opacity: 0.8;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored:
    0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-\[0px_3px_10px_0px_rgba\(42\2c 40\2c 135\2c 0\.05\)\] {
  --tw-shadow: 0px 3px 10px 0px rgba(42, 40, 135, 0.05);
  --tw-shadow-colored: 0px 3px 10px 0px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-\[0px_4px_10px_0px_rgba\(51\2c 37\2c 109\2c 0\.07\)\] {
  --tw-shadow: 0px 4px 10px 0px rgba(51, 37, 109, 0.07);
  --tw-shadow-colored: 0px 4px 10px 0px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-\[0px_6px_10px_0px_rgba\(42\2c _40\2c _135\2c _0\.05\)\] {
  --tw-shadow: 0px 6px 10px 0px rgba(42, 40, 135, 0.05);
  --tw-shadow-colored: 0px 6px 10px 0px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-inset-dialog-gray {
  --tw-shadow: 0px 0px 0px 1px var(--color-neutral-200) inset;
  --tw-shadow-colored: inset 0px 0px 0px 1px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored:
    0 10px 15px -3px var(--tw-shadow-color),
    0 4px 6px -4px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored:
    0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:
    var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
    var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate)
    var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-\[2px\] {
  --tw-backdrop-blur: blur(2px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
    var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
    var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
    var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
    var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
    var(--tw-backdrop-sepia);
}

.transition {
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    -webkit-backdrop-filter;
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke,
    opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property:
    color,
    background-color,
    border-color,
    text-decoration-color,
    fill,
    stroke,
    opacity,
    box-shadow,
    transform,
    filter,
    backdrop-filter,
    -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.delay-700 {
  transition-delay: 700ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(
        var(--tw-enter-translate-x, 0),
        var(--tw-enter-translate-y, 0),
        0
      )
      scale3d(
        var(--tw-enter-scale, 1),
        var(--tw-enter-scale, 1),
        var(--tw-enter-scale, 1)
      )
      rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(
        var(--tw-exit-translate-x, 0),
        var(--tw-exit-translate-y, 0),
        0
      )
      scale3d(
        var(--tw-exit-scale, 1),
        var(--tw-exit-scale, 1),
        var(--tw-exit-scale, 1)
      )
      rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.fade-in-0 {
  --tw-enter-opacity: 0;
}

.zoom-in-95 {
  --tw-enter-scale: 0.95;
}

.duration-1000 {
  animation-duration: 1000ms;
}

.duration-200 {
  animation-duration: 200ms;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.delay-700 {
  animation-delay: 700ms;
}

.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

/* Hide scrollbar for Chrome, Safari and Opera */

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */

.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.underline-from-font {
  text-underline-position: from-font;
}

.\[clip-path\:circle\(50\%\)\] {
  clip-path: circle(50%);
}

/* Our configs */

/* Color design system */

/* Link => https://www.figma.com/file/qRNfEkalc67TUhyFm6AjFf/ESTO-Design-System?type=design&node-id=1-6481&mode=design&t=cv07M8IvBhRx2JAL-0 */

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-normal::file-selector-button {
  font-weight: 400;
}

.placeholder\:text-neutral-400::placeholder {
  color: var(--color-neutral-400);
}

.placeholder\:text-red-500::placeholder {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.placeholder\:opacity-0::placeholder {
  opacity: 0;
}

.autofill\:\!bg-transparent:-webkit-autofill {
  background-color: transparent !important;
}

.autofill\:\!bg-transparent:autofill {
  background-color: transparent !important;
}

.hover\:border-neutral-200:hover {
  border-color: var(--color-neutral-200);
}

.hover\:bg-black\/90:hover {
  background-color: rgb(0 0 0 / 0.9);
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-neutral-100:hover {
  background-color: var(--color-neutral-100);
}

.hover\:bg-primary-black:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * var(--tw-bg-opacity, 1))
  );
}

.hover\:bg-primary-brand02:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(
    in srgb,
    var(--color-primary-brand-02),
    transparent calc(100% - 100% * var(--tw-bg-opacity, 1))
  );
}

.hover\:bg-transparent:hover {
  background-color: transparent;
}

.hover\:text-neutral-200:hover {
  color: var(--color-neutral-200);
}

.hover\:text-primary-brand02:hover {
  --tw-text-opacity: 1;
  color: color-mix(
    in srgb,
    var(--color-primary-brand-02),
    transparent calc(100% - 100% * var(--tw-text-opacity, 1))
  );
}

.hover\:text-primary-white:hover {
  --tw-text-opacity: 1;
  color: color-mix(
    in srgb,
    var(--color-primary-white),
    transparent calc(100% - 100% * var(--tw-text-opacity, 1))
  );
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:shadow-none:hover {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:placeholder\:opacity-100:focus::placeholder {
  opacity: 1;
}

.focus-visible\:border-system-red:focus-visible {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-system-red),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:
    var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:
    var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:
    var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-0:focus-visible {
  --tw-ring-offset-width: 0px;
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:cursor-wait:disabled {
  cursor: wait;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group.toaster .group-\[\.toaster\]\:border-border {
  border-color: hsl(var(--border));
}

.group.toaster .group-\[\.toaster\]\:bg-background {
  background-color: hsl(var(--background));
}

.group.toast .group-\[\.toast\]\:text-\[\.875rem\] {
  font-size: 0.875rem;
}

.group.toast .group-\[\.toast\]\:font-normal {
  font-weight: 400;
}

.group.toast .group-\[\.toast\]\:font-semibold {
  font-weight: 600;
}

.group.toast .group-\[\.toast\]\:\!leading-\[1\.125rem\] {
  line-height: 1.125rem !important;
}

.group.toast .group-\[\.toast\]\:\!tracking-\[-\.00875rem\] {
  letter-spacing: -0.00875rem !important;
}

.group.toast .group-\[\.toast\]\:tracking-\[-\.00875rem\] {
  letter-spacing: -0.00875rem;
}

.group.toaster .group-\[\.toaster\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group.toaster .group-\[\.toaster\]\:shadow-lg {
  --tw-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored:
    0 10px 15px -3px var(--tw-shadow-color),
    0 4px 6px -4px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[side\=bottom\]\:translate-y-1[data-side='bottom'] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side='left'] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side='right'] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side='top'] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-5[data-state='checked'] {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=unchecked\]\:translate-x-0[data-state='unchecked'] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}

.data-\[state\=closed\]\:animate-accordion-up[data-state='closed'] {
  animation: accordion-up 0.2s ease-out;
}

@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

.data-\[state\=open\]\:animate-accordion-down[data-state='open'] {
  animation: accordion-down 0.2s ease-out;
}

.data-\[disabled\]\:bg-neutral-100[data-disabled] {
  background-color: var(--color-neutral-100);
}

.data-\[state\=checked\]\:bg-neutral-100[data-state='checked'] {
  background-color: var(--color-neutral-100);
}

.data-\[state\=checked\]\:bg-primary[data-state='checked'] {
  background-color: hsl(var(--primary));
}

.data-\[state\=open\]\:bg-accent[data-state='open'] {
  background-color: hsl(var(--accent));
}

.data-\[state\=unchecked\]\:bg-input[data-state='unchecked'] {
  background-color: hsl(var(--input));
}

.data-\[state\=open\]\:text-muted-foreground[data-state='open'] {
  color: hsl(var(--muted-foreground));
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}

.data-\[state\=disabled\]\:opacity-50[data-state='disabled'] {
  opacity: 0.5;
}

.data-\[state\=open\]\:animate-in[data-state='open'] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state='closed'] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:fade-out-0[data-state='closed'] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state='open'] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state='closed'] {
  --tw-exit-scale: 0.95;
}

.data-\[state\=open\]\:zoom-in-95[data-state='open'] {
  --tw-enter-scale: 0.95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side='bottom'] {
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side='left'] {
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side='right'] {
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side='top'] {
  --tw-enter-translate-y: 0.5rem;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state='closed'] {
  --tw-exit-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state='open'] {
  --tw-enter-translate-x: -50%;
}

.disabled\:data-\[is-loading\=\"false\"\]\:border-neutral-400[data-is-loading='false']:disabled {
  border-color: var(--color-neutral-400);
}

.disabled\:data-\[is-loading\=\"false\"\]\:bg-neutral-400[data-is-loading='false']:disabled {
  background-color: var(--color-neutral-400);
}

.disabled\:data-\[is-loading\=\"false\"\]\:text-neutral-300[data-is-loading='false']:disabled {
  color: var(--color-neutral-300);
}

@media (min-width: 375px) {
  .min-\[375px\]\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
      rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
      scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

@media (min-width: 390px) {
  .xs\:h-\[25rem\] {
    height: 25rem;
  }
}

@media (min-width: 768px) {
  .md\:relative {
    position: relative;
  }

  .md\:top-\[50\%\] {
    top: 50%;
  }

  .md\:m-auto {
    margin: auto;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-\[0\.025rem\] {
    margin-top: 0.025rem;
  }

  .md\:block {
    display: block;
  }

  .md\:size-full {
    width: 100%;
    height: 100%;
  }

  .md\:h-\[5rem\] {
    height: 5rem;
  }

  .md\:h-\[95px\] {
    height: 95px;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:w-\[calc\(100\%-3rem\)\] {
    width: calc(100% - 3rem);
  }

  .md\:max-w-\[22rem\] {
    max-width: 22rem;
  }

  .md\:max-w-\[37\.5rem\] {
    max-width: 37.5rem;
  }

  .md\:max-w-\[45rem\] {
    max-width: 45rem;
  }

  .md\:translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
      rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
      scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:rounded-b-xxl {
    border-bottom-right-radius: 1.5rem;
    border-bottom-left-radius: 1.5rem;
  }

  .md\:border-neutral-200 {
    border-color: var(--color-neutral-200);
  }

  .md\:bg-neutral-50 {
    background-color: var(--color-neutral-50);
  }

  .md\:bg-transparent {
    background-color: transparent;
  }

  .md\:bg-center {
    background-position: center;
  }

  .md\:bg-no-repeat {
    background-repeat: no-repeat;
  }

  .md\:p-0 {
    padding: 0px;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-\[2rem\] {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:shadow-container {
    --tw-shadow: 0px 5px 20px 0px rgba(42, 40, 135, 0.05);
    --tw-shadow-colored: 0px 5px 20px 0px var(--tw-shadow-color);
    box-shadow:
      var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow);
  }

  .md\:grid-areas-\[left_right\] {
    grid-template-areas: 'left right';
  }

  .md\:grid-in-\[left\] {
    grid-area: left;
  }

  .md\:grid-in-\[right\] {
    grid-area: right;
  }

  .md\:data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state='closed'] {
    --tw-exit-translate-y: -48%;
  }

  .md\:data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state='open'] {
    --tw-enter-translate-y: -48%;
  }
}

@media (min-width: 1024px) {
  .lg\:top-\[12\.5rem\] {
    top: 12.5rem;
  }

  .lg\:m-auto {
    margin: auto;
  }

  .lg\:h-\[25rem\] {
    height: 25rem;
  }

  .lg\:w-full {
    width: 100%;
  }
}

.\[\&\:focus-visible\]\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.\[\&\:has\(button\[role\=combobox\]\[data-state\=open\]\)\]\:border-primary-black:has(
  button[role='combobox'][data-state='open']
) {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

.\[\&\:has\(button\[role\=combobox\]\[data-state\=open\]\)\]\:border-system-red:has(
  button[role='combobox'][data-state='open']
) {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-system-red),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

.\[\&\:has\(input\:focus-visible\)\]\:border-solid:has(input:focus-visible) {
  border-style: solid;
}

.\[\&\:has\(input\:focus-visible\)\]\:border-primary-black:has(
  input:focus-visible
) {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

.\[\&\:has\(input\[aria-invalid\=true\]\)\]\:border-system-red:has(
  input[aria-invalid='true']
) {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-system-red),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

.\[\&\:has\(input\[aria-invalid\=true\]\:focus-visible\)\]\:border-system-red:has(
  input[aria-invalid='true']:focus-visible
) {
  --tw-border-opacity: 1;
  border-color: color-mix(
    in srgb,
    var(--color-system-red),
    transparent calc(100% - 100% * var(--tw-border-opacity, 1))
  );
}

@media (min-width: 768px) {
  .md\:\[\&\>\*\:first-child\]\:pb-\[5\.125rem\] > *:first-child {
    padding-bottom: 5.125rem;
  }

  .md\:\[\&\>\*\:first-child\]\:pt-\[5\.5rem\] > *:first-child {
    padding-top: 5.5rem;
  }
}

.\[\&\>span\]\:w-1 > span {
  width: 0.25rem;
}

.\[\&\>span\]\:flex-1 > span {
  flex: 1 1 0%;
}

.\[\&\>span\]\:truncate > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.\[\&\[data-state\=closed\]\>svg\.plus\]\:inline[data-state='closed']
  > svg.plus {
  display: inline;
}

.\[\&\[data-state\=open\]\>svg\.minus\]\:inline[data-state='open'] > svg.minus {
  display: inline;
}

.\[\&\[role\=combobox\]\[data-state\=open\]\>svg\]\:rotate-180[role='combobox'][data-state='open']
  > svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y))
    rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
    scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\[role\=combobox\]\]\:w-full[role='combobox'] {
  width: 100%;
}

.\[\&_\#rightBody\]\:rounded-t-xxl #rightBody {
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
}

.\[\&_\#rightBody\]\:border-t #rightBody {
  border-top-width: 1px;
}

.\[\&_\#rightBody\]\:border-solid #rightBody {
  border-style: solid;
}

.\[\&_\#rightBody\]\:border-neutral-200 #rightBody {
  border-color: var(--color-neutral-200);
}

.\[\&_\#rightBody\]\:shadow-\[0px_-5px_15px_0px_rgba\(42\2c
  40\2c
  135\2c
  0\.05\)\]
  #rightBody {
  --tw-shadow: 0px -5px 15px 0px rgba(42, 40, 135, 0.05);
  --tw-shadow-colored: 0px -5px 15px 0px var(--tw-shadow-color);
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow);
}
