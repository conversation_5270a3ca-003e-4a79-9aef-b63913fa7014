import type { SmallLoanRoutePaths } from 'app-constants';
import { useLocation } from 'react-router-dom';

import {
  SmallLoanStepsCount,
  SmallLoanWithProgressBarRoutePaths,
} from './config';
import { ProgressBar } from './ProgressBar';

export const SmallLoanProgressBar = ({ className }: { className?: string }) => {
  const location = useLocation();
  const [, , pagePath] = location.pathname.split('/');

  const isVisible = SmallLoanWithProgressBarRoutePaths.includes(
    pagePath as SmallLoanRoutePaths,
  );

  if (!isVisible) return null;

  const totalStepCount = Object.keys(SmallLoanStepsCount).length;
  const currentStepCount = SmallLoanStepsCount[pagePath as SmallLoanRoutePaths];

  return (
    <ProgressBar
      currentStepCount={currentStepCount}
      totalStepCount={totalStepCount}
      className={className}
    />
  );
};
