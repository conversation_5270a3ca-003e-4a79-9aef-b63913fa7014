import { SmallLoanRoutePaths } from 'app-constants';
import type { AnyObject } from 'models/core.models';

export const SmallLoanStepsCount: AnyObject = {
  [SmallLoanRoutePaths.CHECKOUT]: 0,
  [SmallLoanRoutePaths.CONTACT]: 1,
  [SmallLoanRoutePaths.PURPOSE_OF_LOAN]: 2,
  [SmallLoanRoutePaths.CONTACT_EXTRA]: 3,
  [SmallLoanRoutePaths.ACCOUNT_SCORING]: 4,
  [SmallLoanRoutePaths.INCOME_VERIFICATION]: 5,
  [SmallLoanRoutePaths.EMTA_CONSENT]: 6,
  [SmallLoanRoutePaths.SIGNING]: 7,
};

export const SmallLoanWithProgressBarRoutePaths = [
  SmallLoanRoutePaths.CHECKOUT,
  SmallLoanRoutePaths.CONTACT,
  SmallLoanRoutePaths.PURPOSE_OF_LOAN,
  SmallLoanRoutePaths.CONTACT_EXTRA,
  SmallLoanRoutePaths.ACCOUNT_SCORING,
  SmallLoanRoutePaths.INCOME_VERIFICATION,
  SmallLoanRoutePaths.EMTA_CONSENT,
];
