import { cn } from 'utils/tailwind';

type ProgressBarProps = {
  currentStepCount: number;
  totalStepCount: number;
  className?: string;
};

export const ProgressBar = ({
  currentStepCount,
  totalStepCount,
  className,
}: ProgressBarProps) => {
  const progressPercentage =
    totalStepCount === 0 ? 0 : (currentStepCount / totalStepCount) * 100;

  return (
    <div className={cn(className, 'flex justify-center w-full')}>
      <div className="w-full flex flex-col justify-end pb-4 md:px-0 max-w-100 md:max-w-full">
        <div className="relative w-full h-2 bg-muted rounded-full">
          <div
            className="absolute top-0 left-0 h-2 bg-primary rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </div>
  );
};
