import type { CreditLineRoutePaths } from 'app-constants';
import { useLocation } from 'react-router-dom';

import { ProgressBar } from '../ProgressBar';
import {
  CreditLineStepsCount,
  CreditLineWithProgressBarRoutePaths,
} from './config';

export const CreditLineProgressBar = () => {
  const location = useLocation();
  const [, , pagePath] = location.pathname.split('/');

  const isVisible = CreditLineWithProgressBarRoutePaths.includes(
    pagePath as CreditLineRoutePaths,
  );

  if (!isVisible) return null;

  const totalStepCount = Object.keys(CreditLineStepsCount).length;
  const currentStepCount =
    CreditLineStepsCount[pagePath as CreditLineRoutePaths];

  return (
    <ProgressBar
      currentStepCount={currentStepCount}
      totalStepCount={totalStepCount}
    />
  );
};
