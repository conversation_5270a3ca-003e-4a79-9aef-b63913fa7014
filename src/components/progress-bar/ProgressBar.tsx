type ProgressBarProps = {
  currentStepCount: number;
  totalStepCount: number;
};

export const ProgressBar = ({
  currentStepCount,
  totalStepCount,
}: ProgressBarProps) => {
  const progressPercentage =
    totalStepCount === 0 ? 0 : (currentStepCount / totalStepCount) * 100;

  return (
    <>
      <div className="px-[1.5rem] flex justify-center md:px-[2rem]">
        <div className="w-full flex flex-col justify-end max-w-[25rem] h-[50px] md:h-[95px] pb-4">
          <div className="relative w-full h-2 bg-muted rounded-full">
            <div
              className="absolute top-0 left-0 h-2 bg-primary rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </div>
    </>
  );
};
