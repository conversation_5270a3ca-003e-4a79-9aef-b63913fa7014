import { InfoTooltip } from 'components/info-tooltip';
import { Typography } from 'components/typography';
import CampaignIcon from 'icons/campaign.svg?react';
import { cn } from 'utils/tailwind';

type CampaignBannerProps = {
  title?: string;
  description?: string;
  tooltip?: React.ReactNode;
  info?: string;
  className?: string;
  extraInfo?: string;
};
export const CampaignBanner = ({
  title,
  description,
  info,
  className,
  extraInfo,
}: CampaignBannerProps) => {
  return (
    <div className={cn('max-w-3xl mx-auto flex flex-col gap-4', className)}>
      <div
        className={cn(
          'p-[0.875rem] bg-[#fbf9ec] w-full bg-center bg-cover rounded-2xl flex justify-between items-center',
          extraInfo && 'rounded-b-none',
        )}
        style={{
          backgroundImage: 'url(/src/img/campaign-bg.webp)',
        }}
      >
        <div className="flex items-center gap-2">
          <CampaignIcon className="w-4 h-4 text-[#ffce84]" />
          <div className="flex flex-col">
            <Typography variant="text-s" affects="medium">
              {title && title}
            </Typography>
          </div>
        </div>
        {info ? (
          <InfoTooltip
            className={'w-fit max-w-72'}
            text={info}
            iconClassName="h-4 w-4 text-primary-black"
          />
        ) : null}
      </div>
      {extraInfo ? (
        <div className="-mt-4 bg-[#fbf9ec] p-[0.875rem] rounded-2xl rounded-t-none border-t-[2px] border-solid border-[#FEDBA3]">
          <Typography variant="text-s" className="text-neutral-500">
            {extraInfo}
          </Typography>
        </div>
      ) : null}
      <Typography variant="text-s" className="text-neutral-500">
        {description && description}
      </Typography>
    </div>
  );
};
