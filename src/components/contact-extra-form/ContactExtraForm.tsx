import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import { FormSelectField } from 'components/form/form-select-field';
import { WarningNotification } from 'components/notification';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { isLtRegion } from 'environment';
import type { Option } from 'models';
import type { ReactNode } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

// Define a specific type for the form data keys
export type ContactExtraFormFields = {
  [FormFieldNames.occupationCategory]: string | null;
  [FormFieldNames.netIncomeMonthly]: number | null;
  [FormFieldNames.expenditureMonthly]: number | null;
  [FormFieldNames.monthlyLivingExpenses]: number | null;
  [FormFieldNames.numberOfDependents]: number | null;
  [FormFieldNames.employmentDate]: string | null;
  [FormFieldNames.overdueDebt]: number | null;
  [FormFieldNames.planningNewDebts]: number | null;
  [FormFieldNames.futureReducedEarnings]: number | null;
  [FormFieldNames.ultimateBeneficialOwner]: boolean;
  [FormFieldNames.addLegalPersonToInvoice]: boolean;
  [FormFieldNames.legalPerson]: string | null;
};
export interface ContactExtraFormData
  extends FieldValues,
    ContactExtraFormFields {}

export interface ContactExtraFormProps {
  form: UseFormReturn<ContactExtraFormData, any>;
  onSubmit: (data: ContactExtraFormData) => void | Promise<void>;
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  occupationCategoryOptions: Option[];
  employmentDateOptions: Option[];
  isSubmitting?: boolean;
  isNavigating?: boolean;
  onBack?: () => void;
  children?: ReactNode;
  className?: string;
}

export const ContactExtraForm = ({
  form,
  onSubmit,
  visiblePageAttributes,
  validationErrors,
  occupationCategoryOptions,
  employmentDateOptions,
  isSubmitting = false,
  isNavigating = false,
  onBack,
  children,
  className,
}: ContactExtraFormProps) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={`grid w-full gap-2 ${className || ''}`}
      >
        {visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer] ? (
          <WarningNotification className="mb-10">
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.checkIncomeDisclaimer2)}
          </WarningNotification>
        ) : null}

        {visiblePageAttributes[
          PageAttributeNames.occupationCategoryDropdown
        ] ? (
          <FormSelectField<ContactExtraFormData>
            control={form.control}
            name={FormFieldNames.occupationCategory}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryFieldLabel,
            )}
            options={occupationCategoryOptions}
            invalid={
              validationErrors[FormFieldNames.occupationCategory] ||
              !!form.getFieldState(FormFieldNames.occupationCategory).error
            }
            disabled={isSubmitting || !occupationCategoryOptions.length}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.netIncomeMonthly] ? (
          <FormNumberInputField<ContactExtraFormData>
            control={form.control}
            disabled={isSubmitting}
            name={FormFieldNames.netIncomeMonthly}
            label={t(
              isLtRegion
                ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabelLt
                : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabel,
            )}
            info={t(
              isLtRegion
                ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabelLt
                : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabel,
            )}
            invalid={
              validationErrors[FormFieldNames.netIncomeMonthly] ||
              !!form.getFieldState(FormFieldNames.netIncomeMonthly).error
            }
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.expenditureMonthly] ? (
          <FormNumberInputField<ContactExtraFormData>
            control={form.control}
            disabled={isSubmitting}
            name={FormFieldNames.expenditureMonthly}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureLabel)}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureTooltipLabel,
            )}
            invalid={
              validationErrors[FormFieldNames.expenditureMonthly] ||
              !!form.getFieldState(FormFieldNames.expenditureMonthly).error
            }
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.monthlyLivingExpenses] ? (
          <FormNumberInputField<ContactExtraFormData>
            control={form.control}
            disabled={isSubmitting}
            name={FormFieldNames.monthlyLivingExpenses}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthLabel,
            )}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthTooltipLabel,
            )}
            invalid={
              validationErrors[FormFieldNames.monthlyLivingExpenses] ||
              !!form.getFieldState(FormFieldNames.monthlyLivingExpenses).error
            }
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.numberOfDependents] ? (
          <FormNumberInputField<ContactExtraFormData>
            control={form.control}
            name={FormFieldNames.numberOfDependents}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.dependentsLabel)}
            allowLeadingZeros
            fixedDecimalScale={true}
            disabled={isSubmitting}
            invalid={
              validationErrors[FormFieldNames.numberOfDependents] ||
              !!form.getFieldState(FormFieldNames.numberOfDependents).error
            }
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.employmentDate] ? (
          <FormSelectField<ContactExtraFormData>
            control={form.control}
            name={FormFieldNames.employmentDate}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.employmentDateLabel)}
            options={employmentDateOptions}
            disabled={isSubmitting}
            invalid={
              validationErrors[FormFieldNames.employmentDate] ||
              !!form.getFieldState(FormFieldNames.employmentDate).error
            }
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.overdueDebt] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(FormFieldNames.overdueDebt) ||
              validationErrors[FormFieldNames.overdueDebt]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(FormFieldNames.overdueDebt, null);
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtCheckboxLabel,
            )}
            disabled={isSubmitting}
            invalid={
              validationErrors[FormFieldNames.overdueDebt] ||
              !!form.getFieldState(FormFieldNames.overdueDebt).error
            }
          >
            <FormNumberInputField<ContactExtraFormData>
              control={form.control}
              name={FormFieldNames.overdueDebt}
              label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtLabel)}
              info={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtTooltipLabel,
              )}
              disabled={isSubmitting}
              invalid={
                validationErrors[FormFieldNames.overdueDebt] ||
                !!form.getFieldState(FormFieldNames.overdueDebt).error
              }
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.planningNewDebts] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(FormFieldNames.planningNewDebts) ||
              validationErrors[FormFieldNames.planningNewDebts]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(FormFieldNames.planningNewDebts, null);
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsCheckboxLabel,
            )}
            disabled={isSubmitting}
            invalid={
              validationErrors[FormFieldNames.planningNewDebts] ||
              !!form.getFieldState(FormFieldNames.planningNewDebts).error
            }
          >
            <FormNumberInputField<ContactExtraFormData>
              control={form.control}
              name={FormFieldNames.planningNewDebts}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsLabel,
              )}
              disabled={isSubmitting}
              invalid={
                validationErrors[FormFieldNames.planningNewDebts] ||
                !!form.getFieldState(FormFieldNames.planningNewDebts).error
              }
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.futureReducedEarnings] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(FormFieldNames.futureReducedEarnings) ||
              validationErrors[FormFieldNames.futureReducedEarnings]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(FormFieldNames.futureReducedEarnings, null);
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsCheckboxLabel,
            )}
            disabled={isSubmitting}
            invalid={
              validationErrors[FormFieldNames.futureReducedEarnings] ||
              !!form.getFieldState(FormFieldNames.futureReducedEarnings).error
            }
          >
            <FormNumberInputField<ContactExtraFormData>
              control={form.control}
              disabled={isSubmitting}
              name={FormFieldNames.futureReducedEarnings}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsLabel,
              )}
              invalid={
                validationErrors[FormFieldNames.futureReducedEarnings] ||
                !!form.getFieldState(FormFieldNames.futureReducedEarnings).error
              }
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.ultimateBeneficialOwner] ? (
          <FormCheckboxField<ContactExtraFormData>
            containerClassName="mt-2 px-2.5"
            control={form.control}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerLabel,
            )}
            name={FormFieldNames.ultimateBeneficialOwner}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerTooltip,
            )}
            disabled={isSubmitting}
            invalid={
              validationErrors[FormFieldNames.ultimateBeneficialOwner] ||
              !!form.getFieldState(FormFieldNames.ultimateBeneficialOwner).error
            }
          />
        ) : null}

        {children}

        <Button
          className="mt-12"
          disabled={!isSubmitting && isNavigating}
          loading={isSubmitting}
          type="submit"
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>

        {onBack && (
          <Button
            fullWidth
            className="mt-2"
            variant="white"
            loading={!isSubmitting && isNavigating}
            disabled={isSubmitting}
            onClick={onBack}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
        )}
      </form>
    </Form>
  );
};
