import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { FormInputField } from 'components/form/form-input-field';
import { FormPhoneField } from 'components/form/form-phone-field';
import { FormSelectField } from 'components/form/form-select-field';
import { Button } from 'components/ui/button';
import { regionPhonePrefix } from 'environment';
import { useMemo, useState } from 'react';
import type { FieldValues, Path, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
export interface SpouseSendInstructionsFormData extends FieldValues {
  [FormFieldNames.instructionsSendingMethod]?: string | null;
  [FormFieldNames.spouseEmail]?: string | null;
  [FormFieldNames.spousePhone]?: string | null;
}

type SpouseSendInstructionsProps<
  TFormData extends SpouseSendInstructionsFormData,
> = {
  form: UseFormReturn<TFormData>;
  visible?: boolean;
  onSendInstructions: (data: {
    spouse_email?: string;
    spouse_phone?: string;
  }) => Promise<void>;
  sendingConsentLink: boolean;
  validationErrors: Record<string, boolean>;
  instructionsSent: boolean;
  onInstructionsSentChange: (sent: boolean) => void;
  className?: string;
};

export const SpouseSendInstructions = <TFormData extends FieldValues>({
  form,
  visible,
  onSendInstructions,
  sendingConsentLink,
  validationErrors,
  instructionsSent,
  onInstructionsSentChange,
  className,
}: SpouseSendInstructionsProps<TFormData>) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const [sendingMethod, setSendingMethod] = useState<string>('');
  const [spouseEmail, setSpouseEmail] = useState('');
  const [spousePhone, setSpousePhone] = useState('');

  const instructionsSendingMethodOptions = useMemo(
    () => [
      {
        value: 'email',
        label: t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.emailOptionLabel),
      },
      {
        value: 'phone',
        label: t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.phoneOptionLabel),
      },
    ],
    [t],
  );

  if (!visible) {
    return null;
  }

  const instructionsSentDisclaimerText = `${t(
    LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.instructionsSentDisclaimer,
  )} ${spousePhone ? regionPhonePrefix : ''}${spouseEmail || spousePhone}`;

  const handleSendInstructions = async () => {
    const data: { spouse_email?: string; spouse_phone?: string } = {};

    if (sendingMethod === 'email' && spouseEmail) {
      data.spouse_email = spouseEmail;
    } else if (sendingMethod === 'phone' && spousePhone) {
      data.spouse_phone = spousePhone;
    }

    try {
      await onSendInstructions(data);
      onInstructionsSentChange(true);
    } catch (error) {
      console.log(error);
    }
  };

  const handleChange = () => {
    setSendingMethod('');
    setSpouseEmail('');
    setSpousePhone('');
    onInstructionsSentChange(false);
  };

  const canSendInstructions =
    (sendingMethod === 'email' && spouseEmail) ||
    (sendingMethod === 'phone' && spousePhone);

  return (
    <div className={`mt-8 ${className || ''}`}>
      <h3 className="text-lg font-semibold mb-4">
        {t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.spouseSectionTitle)}
      </h3>

      {instructionsSent ? (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-800 mb-2">
            {instructionsSentDisclaimerText}
          </p>
          <Button variant="outline" size="small" onClick={handleChange}>
            {t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.changeInstructionsButton)}
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            {t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.spouseSectionIntro)}
          </p>

          <div className="space-y-4">
            <FormSelectField
              control={form.control}
              name={FormFieldNames.instructionsSendingMethod as Path<TFormData>}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.instructionsSendingMethodLabel,
              )}
              options={instructionsSendingMethodOptions}
              onValueChange={(value) => setSendingMethod(value)}
            />

            {sendingMethod === 'email' && (
              <FormInputField
                control={form.control}
                name={FormFieldNames.spouseEmail as Path<TFormData>}
                label={t(
                  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.spouseEmailFieldLabel,
                )}
                type="email"
                invalid={validationErrors[FormFieldNames.spouseEmail]}
                onChange={(e) => setSpouseEmail(e.target.value)}
              />
            )}

            {sendingMethod === 'phone' && (
              <FormPhoneField
                control={form.control}
                name={FormFieldNames.spousePhone as Path<TFormData>}
                label={t(
                  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.spousePhoneFieldLabel,
                )}
                invalid={validationErrors[FormFieldNames.spousePhone]}
                onChange={(e) => setSpousePhone(e.target.value)}
              />
            )}

            {sendingMethod && (
              <Button
                variant="grey"
                disabled={!canSendInstructions || sendingConsentLink}
                loading={sendingConsentLink}
                onClick={handleSendInstructions}
              >
                {t(
                  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.spouseSectionSubmitButton,
                )}
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
