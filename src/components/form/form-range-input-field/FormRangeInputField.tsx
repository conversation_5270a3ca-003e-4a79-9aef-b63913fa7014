import { FormField, FormItem } from 'components/ui/form';
import { Slider } from 'components/ui/slider';
import { useDebounce } from 'hooks/utils';
import MinusIcon from 'icons/minus-icon.svg?react';
import PlusIcon from 'icons/plus-icon.svg?react';
import { useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import Skeleton from 'react-loading-skeleton';
import { NumericFormat } from 'react-number-format';
import { useUpdateEffect } from 'react-use';
import { cn } from 'utils/tailwind';

type FormRangeInputFieldProps<TFieldValues extends FieldValues = FieldValues> =
  {
    control: Control<TFieldValues>;
    name: Path<TFieldValues>;
    min: number;
    max: number;
    step?: number;
    onAmountButtonsClick?: (operation: 'inc' | 'dec') => () => void;
    onAmountInputBlur?: () => void;
    loading?: boolean;
    disabled?: boolean;
    className?: string;
    customValueOutput?: React.ReactNode;
  };

export const FormRangeInputField = <
  TFieldValues extends FieldValues = FieldValues,
>({
  control,
  name,
  min,
  max,
  step = 10,
  loading,
  disabled,
  className,
  customValueOutput,
}: FormRangeInputFieldProps<TFieldValues>) => {
  const [debouncedAmount, setDebouncedAmount] = useState(0);
  const [isAmountChanged, setIsAmountChanged] = useState(false);

  return (
    <FormField
      control={control}
      name={name}
      render={({
        field: { onChange, value, name, ...field },
        fieldState: { error },
      }) => {
        const isDecButtonDisabled = value <= min || disabled;
        const isIncButtonDisabled = value >= max || disabled;
        const debouncedSetAmount = useDebounce(() => {
          setDebouncedAmount(value);
          setIsAmountChanged(false);
        }, 400);

        useUpdateEffect(() => {
          debouncedSetAmount();
        }, [value]);

        useUpdateEffect(() => {
          if (!isAmountChanged && value !== debouncedAmount) {
            setIsAmountChanged(true);
          }
        });

        const handleAmountButtonsClick = (operation: 'inc' | 'dec') => () => {
          const amountValue = value ?? 0;
          const newValue =
            operation === 'inc' ? amountValue + step : amountValue - step;
          onChange(newValue);
        };

        const handleAmountInputBlur = () => {
          if (!value) {
            onChange(min);
            return;
          }

          if (value < min) {
            onChange(min);
            return;
          }

          if (value > max) {
            onChange(max);
            return;
          }

          if (value % step !== 0) {
            onChange((Math.ceil(value / step) * step).toFixed(0));
            return;
          }
        };

        const valueOutput = customValueOutput ? (
          customValueOutput
        ) : (
          <NumericFormat
            {...field}
            allowLeadingZeros={false}
            allowNegative={false}
            className={cn(
              'text-[2rem] px-0 font-bold leading-[2.25rem] tracking-[-.12rem] w-full rounded-md focus:outline-none',
              'border-transparent',
              error && 'text-red-500 placeholder:text-red-500',
            )}
            fixedDecimalScale
            name={`${name}-input`}
            onValueChange={(v) => {
              const newValue = v.floatValue === undefined ? 0 : v.floatValue;
              onChange(newValue);
            }}
            suffix=" €"
            onBlur={handleAmountInputBlur}
            thousandSeparator=","
            value={value}
            isAllowed={(values) => {
              const { formattedValue } = values;

              if (formattedValue.startsWith('0')) {
                return false;
              }
              return true;
            }}
            disabled={disabled}
          />
        );
        return (
          <FormItem className={className}>
            <div className="grid grid-cols-[1fr_auto] gap-4 items-center">
              {loading ? (
                <Skeleton className="rounded-3xl w-full h-8 animate-pulse-opacity" />
              ) : (
                valueOutput
              )}

              <div className="flex gap-4">
                {loading ? (
                  <>
                    <Skeleton className="rounded-full animate-pulse-opacity h-8 w-8" />
                    <Skeleton className="rounded-full animate-pulse-opacity h-8 w-8" />
                  </>
                ) : (
                  <>
                    <button
                      type="button"
                      onClick={handleAmountButtonsClick('dec')}
                      disabled={isDecButtonDisabled}
                      className={cn(
                        isDecButtonDisabled && 'opacity-40 cursor-not-allowed',
                      )}
                    >
                      <MinusIcon />
                    </button>
                    <button
                      type="button"
                      onClick={handleAmountButtonsClick('inc')}
                      disabled={isIncButtonDisabled}
                      className={cn(
                        isIncButtonDisabled && 'opacity-40 cursor-not-allowed',
                      )}
                    >
                      <PlusIcon />
                    </button>
                  </>
                )}
              </div>
            </div>
            {loading ? (
              <div className="relative !mt-8 flex flex-col items-center">
                <Skeleton
                  containerClassName="w-full"
                  className={cn('rounded-3xl w-full h-5 animate-pulse-opacity')}
                />
                <Skeleton
                  containerClassName="absolute top-[50%] -translate-y-[50%] left-[80%]"
                  className={cn('rounded-xl animate-pulse-opacity h-11 w-11')}
                />
              </div>
            ) : (
              <Slider
                {...field}
                max={max}
                min={min}
                name={`${name}-slider`}
                onValueChange={(value) => {
                  onChange(+value[0].toFixed(0));
                }}
                step={step}
                value={[value]}
                disabled={disabled}
              />
            )}
          </FormItem>
        );
      }}
    />
  );
};
