import {
  FloatingForm<PERSON>abel,
  FormControl,
  FormField,
  FormItem,
} from 'components/ui/form';
import { Input } from 'components/ui/input';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { cn } from 'utils/tailwind';

import styles from './FormInputField.module.css';

type FormInputFieldProps<TFieldValues extends FieldValues = FieldValues> = {
  name: Path<TFieldValues>;
  control: Control<TFieldValues>;
  label: ReactNode;
  invalid?: boolean;
  disabled?: boolean;
  className?: string;
  containerClassName?: string;
  info?: string;
} & Omit<
  React.InputHTMLAttributes<HTMLInputElement>,
  'name' | 'className' | 'disabled'
>;

export const FormInputField = <TFieldValues extends FieldValues = FieldValues>({
  name,
  control,
  label,
  invalid,
  disabled,
  className,
  containerClassName,
  ...props
}: FormInputFieldProps<TFieldValues>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn('w-full relative', containerClassName)}>
          <FormControl>
            <div className="relative">
              <Input
                className={cn(className, styles.autofillWrapper)}
                inputClassName={styles.input}
                invalid={invalid}
                disabled={disabled}
                {...field}
                {...props}
              />
              <FloatingFormLabel
                className={
                  field.value
                    ? styles.floatingLabelFilled
                    : styles.floatingLabel
                }
                filled={!!field.value}
              >
                {label}
              </FloatingFormLabel>
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
};
