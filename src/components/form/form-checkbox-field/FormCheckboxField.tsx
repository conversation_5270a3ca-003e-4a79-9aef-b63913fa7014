import { InfoTooltip } from 'components/info-tooltip';
import { Checkbox } from 'components/ui/checkbox';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from 'components/ui/form';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { cn } from 'utils/tailwind';

type FormCheckboxFieldProps<TFieldValues extends FieldValues = FieldValues> = {
  name: Path<TFieldValues>;
  control: Control<TFieldValues>;
  label: ReactNode;
  invalid?: boolean;
  className?: string;
  containerClassName?: string;
  disabled?: boolean;
  info?: string;
};

export const FormCheckboxField = <
  TFieldValues extends FieldValues = FieldValues,
>({
  name,
  control,
  label,
  invalid,
  className,
  containerClassName,
  disabled,
  info,
}: FormCheckboxFieldProps<TFieldValues>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={cn('rounded-md border', containerClassName)}>
        <div className="flex flex-row cursor-pointer space-y-0">
          <FormControl>
            <Checkbox
              className={cn('mt-[0.125rem]', className)}
              invalid={invalid}
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={disabled}
              {...field}
            />
          </FormControl>
          <div className="w-full flex justify-between space-y-1 leading-none">
            <FormLabel
              className={cn(
                'cursor-pointer pl-3 flex w-full',
                disabled && 'cursor-not-allowed',
              )}
            >
              {label}
            </FormLabel>
            {info ? (
              <div className="flex items-center">
                <InfoTooltip
                  className={cn(disabled && 'cursor-not-allowed')}
                  text={info}
                  iconClassName="h-4 w-4 text-neutral-400"
                />
              </div>
            ) : null}
          </div>
        </div>
      </FormItem>
    )}
  />
);
