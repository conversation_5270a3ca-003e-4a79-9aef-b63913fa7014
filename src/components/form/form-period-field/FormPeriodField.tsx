import { FormField, FormItem } from 'components/ui/form';
import type { SliderSize } from 'components/ui/slider';
import { Slider } from 'components/ui/slider';
import MinusIcon from 'icons/minus-icon.svg?react';
import PlusIcon from 'icons/plus-icon.svg?react';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import Skeleton from 'react-loading-skeleton';
import { cn } from 'utils/tailwind';

import { FormSelectField } from '../form-select-field/FormSelectField';

type FormPeriodFieldProps<TFieldValues extends FieldValues = FieldValues> = {
  name: Path<TFieldValues>;
  control: Control<TFieldValues>;
  label?: ReactNode;
  periodValues: number[];
  invalid?: boolean;
  disabled?: boolean;
  info?: string;
  className?: string;
  containerClassName?: string;
  onPeriodChange?: (formFieldValues: FieldValues) => void;
  showDropdown?: boolean;
  loading?: boolean;
  size?: SliderSize;
};

export const FormPeriodField = <
  TFieldValues extends FieldValues = FieldValues,
>({
  name,
  control,
  label,
  periodValues,
  invalid,
  disabled,
  info,
  className,
  containerClassName,
  onPeriodChange,
  showDropdown = true,
  loading,
  size = 'regular',
}: FormPeriodFieldProps<TFieldValues>) => {
  const options = periodValues.map((value) => ({
    label: `${value} months`,
    value: value.toString(),
  }));

  const minPeriod = periodValues[0];
  const maxPeriod = periodValues[periodValues.length - 1];

  const handleButtonClick = (
    direction: 'decrease' | 'increase',
    fieldValue: string,
    onChange: (value: string) => void,
  ) => {
    const currentValue = fieldValue ? parseInt(fieldValue) : minPeriod;
    const currentIndex = periodValues.indexOf(currentValue);

    if (direction === 'decrease' && currentIndex > 0) {
      const newValue = periodValues[currentIndex - 1].toString();
      onChange(newValue);
      onPeriodChange?.({ [name]: newValue });
    } else if (
      direction === 'increase' &&
      currentIndex < periodValues.length - 1
    ) {
      const newValue = periodValues[currentIndex + 1].toString();
      onChange(newValue);
      onPeriodChange?.({ [name]: newValue });
    }
  };

  const renderSlider = (field: any, onChange: (value: string) => void) => {
    const currentValue = field.value ? parseInt(field.value) : minPeriod;
    const currentIndex = periodValues.indexOf(currentValue);

    return (
      <Slider
        {...field}
        max={periodValues.length - 1}
        min={0}
        name={`${name}-slider`}
        onValueChange={(value) => {
          const periodValue = periodValues[value[0]];
          const newValue = periodValue.toString();
          onChange(newValue);
          onPeriodChange?.({ [name]: newValue });
        }}
        step={1}
        value={[currentIndex >= 0 ? currentIndex : 0]}
        disabled={disabled}
        size={size}
      />
    );
  };

  const renderButtons = (field: any, onChange: (value: string) => void) => {
    const isDecButtonDisabled = field.value
      ? parseInt(field.value) <= minPeriod
      : true;
    const isIncButtonDisabled = field.value
      ? parseInt(field.value) >= maxPeriod
      : true;
    return (
      <div className="flex gap-4">
        <button
          type="button"
          onClick={() => handleButtonClick('decrease', field.value, onChange)}
          disabled={disabled || isDecButtonDisabled}
          className={cn(
            'cursor-pointer',
            (disabled || isDecButtonDisabled) &&
              'opacity-40 cursor-not-allowed',
          )}
        >
          <MinusIcon />
        </button>

        <button
          type="button"
          onClick={() => handleButtonClick('increase', field.value, onChange)}
          disabled={disabled || isIncButtonDisabled}
          className={cn(
            'cursor-pointer',
            isIncButtonDisabled && 'opacity-40 cursor-not-allowed',
          )}
        >
          <PlusIcon />
        </button>
      </div>
    );
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { onChange, ...field } }) => {
        return (
          <FormItem className={cn('w-full relative', containerClassName)}>
            {showDropdown &&
              (loading ? (
                <Skeleton className="rounded-3xl h-12 animate-pulse-opacity w-full" />
              ) : (
                <FormSelectField
                  name={name}
                  control={control}
                  label={label}
                  options={options}
                  invalid={invalid}
                  disabled={disabled}
                  info={info}
                  className={cn(className)}
                  containerClassName={containerClassName}
                  onValueChange={(value) => {
                    onChange(value);
                    onPeriodChange?.({ [name]: value });
                  }}
                />
              ))}

            <div className={showDropdown ? 'mt-4' : ''}>
              <div className="grid grid-cols-[1fr_auto] gap-4 items-center">
                {loading ? (
                  <Skeleton className="rounded-3xl h-9.5 animate-pulse-opacity w-full " />
                ) : (
                  renderSlider(field, onChange)
                )}

                <div className="flex gap-4">
                  {loading ? (
                    <>
                      <Skeleton className="rounded-full animate-pulse-opacity h-8 !w-8" />
                      <Skeleton className="rounded-full animate-pulse-opacity h-8 !w-8" />
                    </>
                  ) : (
                    renderButtons(field, onChange)
                  )}
                </div>
              </div>
            </div>
          </FormItem>
        );
      }}
    />
  );
};
