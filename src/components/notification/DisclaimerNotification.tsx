import { Typography } from 'components/typography';
import DisclaimerIcon from 'icons/disclaimer.svg?react';
import type { PropsWithChildren } from 'react';
import { cn } from 'utils/tailwind';

import { Notification } from './Notification';

interface DisclaimerNotificationProps extends PropsWithChildren {
  className?: string;
}

export const DisclaimerNotification = ({
  children,
  className,
}: DisclaimerNotificationProps) => (
  <Notification
    className={cn(
      className,
      'grid grid-cols-[auto_1fr] gap-2 rounded-lg border-solid border-[0.0625rem] bg-neutral-100 bg-primary-white p-2.5 shadow-[0px_6px_10px_0px_rgba(42,40,135,0.05)]',
    )}
    icon={
      <DisclaimerIcon className="mt-1 size-4.5 text-primary-brand-02 md:mt-[0.025rem]" />
    }
  >
    <Typography tag="div" variant="text-s">
      {children}
    </Typography>
  </Notification>
);
