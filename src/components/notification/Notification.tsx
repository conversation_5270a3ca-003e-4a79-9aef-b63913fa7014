import c from 'clsx';
import { Typography } from 'components/typography';
import InfoIcon from 'icons/info.svg?react';
import type { FC, PropsWithChildren } from 'react';

interface NotificationProps extends PropsWithChildren {
  className?: string;
  icon?: React.ReactNode;
}

export const Notification: FC<NotificationProps> = ({
  children,
  className,
  icon = (
    <InfoIcon className="mt-1 size-[1.125rem] text-primary-brand02 md:mt-[0.025rem]" />
  ),
}) => (
  <div
    className={c(
      'grid grid-cols-[auto,1fr] gap-2 rounded-lg border bg-neutral-100 bg-primary-white p-2.5 shadow-[0px_6px_10px_0px_rgba(42,_40,_135,_0.05)]',
      className,
    )}
  >
    {icon}
    <Typography tag="div" variant="text-s">
      {children}
    </Typography>
  </div>
);
