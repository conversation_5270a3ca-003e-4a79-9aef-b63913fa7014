import * as SliderPrimitive from '@radix-ui/react-slider';
import { cva } from 'class-variance-authority';
import SliderHandleIcon from 'icons/slider-handle.svg?react';
import * as React from 'react';
import { cn } from 'utils/tailwind';

type SliderSize = 'regular' | 'small';

export const sliderThumbVariants = cva<{
  size: Record<SliderSize, string>;
}>(
  'flex cursor-grab justify-center focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0',
  {
    variants: {
      size: {
        regular: 'size-10',
        small: 'size-8',
      },
    },
    defaultVariants: {
      size: 'regular',
    },
  },
);

export const sliderTrackVariants = cva<{
  size: Record<SliderSize, string>;
}>('relative w-full grow overflow-hidden rounded-full bg-neutral-100', {
  variants: {
    size: {
      regular: 'h-5 border-4 border-neutral-100',
      small: 'h-3',
    },
  },
  defaultVariants: {
    size: 'regular',
  },
});

type SliderProps = React.ComponentPropsWithoutRef<
  typeof SliderPrimitive.Root
> & {
  size?: SliderSize;
};

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  SliderProps
>(({ className, size = 'regular', ...props }, ref) => {
  return (
    <SliderPrimitive.Root
      ref={ref}
      className={cn(
        'relative flex w-full touch-none select-none items-center py-2.5 !mt-4',
        className,
        props.disabled && 'cursor-not-allowed opacity-80',
      )}
      {...props}
    >
      <SliderPrimitive.Track className={sliderTrackVariants({ size })}>
        <SliderPrimitive.Range className="absolute top-1/2 !left-[0.3rem] -translate-y-1/2 h-[0.750rem] rounded-l-full bg-system-green" />
      </SliderPrimitive.Track>
      <SliderPrimitive.Thumb className={sliderThumbVariants({ size })}>
        <SliderHandleIcon className="size-full" />
      </SliderPrimitive.Thumb>
    </SliderPrimitive.Root>
  );
});
Slider.displayName = SliderPrimitive.Root.displayName;

export { Slider, type SliderProps };
