import * as DialogPrimitive from '@radix-ui/react-dialog';
import { typographyVariants } from 'components/typography';
import { useIsMobileView } from 'hooks/system';
import CloseIcon from 'icons/close-cancel.svg?react';
import * as React from 'react';
import { cn } from 'utils/tailwind';

const Dialog = DialogPrimitive.Root;

const DialogTrigger = DialogPrimitive.Trigger;

const DialogPortal = DialogPrimitive.Portal;

const DialogClose = DialogPrimitive.Close;

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[200] bg-black/20 backdrop-blur-[2px] data-[state=closed]:animate-out data-[state=open]:animate-in',
      className,
    )}
    {...props}
  />
));
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
    title?: string;
    hideCloseButton?: boolean;
    customCloseButton?: React.ReactNode;
    containerClassName?: string;
  }
>(
  (
    {
      className,
      containerClassName,
      children,
      hideCloseButton,
      customCloseButton,
      title,
      ...props
    },
    ref,
  ) => {
    const headerRef = React.useRef<HTMLDivElement>(null);
    const [headerHeight, setHeaderHeight] = React.useState(0);
    const isMobileView = useIsMobileView();

    return (
      <DialogPortal>
        <DialogOverlay />
        <DialogPrimitive.Content
          aria-describedby={undefined}
          onOpenAutoFocus={(e) => {
            e.preventDefault();
            // Measure height after dialog opens
            if (headerRef.current) {
              setHeaderHeight(headerRef.current.offsetHeight);
            }
          }}
          className={cn(
            `font-inter fixed w-full h-full top-[50%] translate-y-[-50%] left-[50%] translate-x-[-50%] !pointer-events-none z-[200] 
          data-[state=closed]:fade-out-0 
          data-[state=open]:fade-in-0 
          data-[state=closed]:zoom-out-95 
          data-[state=open]:zoom-in-95 
          data-[state=closed]:slide-out-to-left-1/2 
          md:data-[state=closed]:slide-out-to-top-[48%] 
          data-[state=open]:slide-in-from-left-1/2 
          md:data-[state=open]:slide-in-from-top-[48%] 
          data-[state=closed]:animate-out 
          data-[state=open]:animate-in
        `,
            containerClassName,
          )}
          ref={ref}
          {...props}
        >
          <div
            className={cn(
              `fixed pointer-events-auto h-auto min-h-fit left-[50%] grid w-full  translate-x-[-50%] border-solid border-[0.25rem] border-neutral-50 bg-background py-8 shadow-inset-dialog-gray duration-200 rounded-xxl
          max-w-unset md:max-w-[37.5rem]
          rounded-b-none md:rounded-b-xxl
          top-unset md:top-[50%]
          bottom-0 md:bottom-unset
          translate-y-0 md:translate-y-[-50%]
          `,
              className,
            )}
          >
            <div
              ref={headerRef}
              className="sticky flex items-center top-0 px-[1.75rem] pb-4 bg-background border-solid mx-[0.25rem]"
            >
              <DialogTitle>{title && title}</DialogTitle>
              {!hideCloseButton && (
                <DialogPrimitive.Close className="ml-auto cursor-pointer opacity-70 transition-opacity hover:opacity-100 outline-none disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                  {customCloseButton ?? <CloseIcon />}
                  <span className="sr-only">Close</span>
                </DialogPrimitive.Close>
              )}
            </div>
            <div
              className="px-8 overflow-y-auto"
              style={{
                maxHeight: isMobileView
                  ? `calc(100vh - 5.5rem - ${headerHeight}px - 2rem)` // viewport height - top indent - dialog header height - dialog top/bottom padding
                  : `calc(75vh - ${headerHeight}px - 2rem)`, // viewport height - dialog header height - dialog top/bottom padding
              }}
            >
              {children}
            </div>
          </div>
        </DialogPrimitive.Content>
      </DialogPortal>
    );
  },
);
DialogContent.displayName = DialogPrimitive.Content.displayName;

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col space-y-1.5 text-center sm:text-left',
      className,
    )}
    {...props}
  />
);
DialogHeader.displayName = 'DialogHeader';

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',
      className,
    )}
    {...props}
  />
);
DialogFooter.displayName = 'DialogFooter';

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      typographyVariants({ variant: 'xxs', affects: 'semibold' }),
      className,
    )}
    {...props}
  />
));
DialogTitle.displayName = DialogPrimitive.Title.displayName;

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn('text-muted-foreground text-sm', className)}
    {...props}
  />
));
DialogDescription.displayName = DialogPrimitive.Description.displayName;

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
};
