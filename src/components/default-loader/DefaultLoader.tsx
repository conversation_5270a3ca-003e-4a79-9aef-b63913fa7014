import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { CircleLoader } from 'components/circle-loader';
import { Typography } from 'components/typography';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

export const DefaultLoader = () => {
  const { t } = useTranslation(LocizeNamespaces.common);

  const [shouldShowDelayedMessage, setShouldShowDelayedMessage] =
    useState(false);

  useEffect(() => {
    setTimeout(() => {
      setShouldShowDelayedMessage(true);
    }, 10000);
  }, []);

  return (
    <div className="font-inter">
      <CircleLoader className="scale-[70%] w-full min-[375px]:scale-100" />

      <div className="min-h-[6.25rem]">
        <Typography
          className={cn(
            'text-center max-h-[100px] opacity-100 transition-all duration-1000 ease',
            shouldShowDelayedMessage && 'max-h-0 opacity-0',
          )}
          variant="xs"
        >
          {t(LOCIZE_COMMON_TRANSLATION_KEYS.defaultDelayedMessageText)}
        </Typography>

        <div
          className={cn(
            'text-center max-h-0 opacity-0 transition-all duration-1000 ease delay-700 flex flex-col gap-4',
            shouldShowDelayedMessage && 'max-h-[100px] opacity-100',
          )}
        >
          <Typography variant="xs">
            {t(LOCIZE_COMMON_TRANSLATION_KEYS.defaultDelayedMessageTitle)}
          </Typography>

          <Typography variant="text-s">
            {t(LOCIZE_COMMON_TRANSLATION_KEYS.defaultDelayedMessageText)}
          </Typography>
        </div>
      </div>
    </div>
  );
};
