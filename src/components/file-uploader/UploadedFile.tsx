import { DotsLoader } from 'components/dots-loader/Dotsloader';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import TrashIcon from 'icons/trash.svg?react';
import { PaperclipIcon } from 'lucide-react';

type UploadedFileProps = {
  name: string;
  id: number;
  deletingLoading: boolean;
  onRemove: () => void;
};

export const UploadedFile = ({
  name,
  id,
  onRemove,
  deletingLoading,
}: UploadedFileProps) => {
  if (deletingLoading) {
    return <DotsLoader size="md" className="pt-3" />;
  }

  return (
    <div
      key={id}
      className={
        'max-h-[500px] opacity-100  p-2 rounded-lg bg-neutral-50 border border-solid border-neutral-200 flex items-center gap-2 mt-6'
      }
    >
      <PaperclipIcon className="w-4 h-4 flex-shrink-0" />
      <Typography
        className="truncate flex-1 min-w-0"
        variant="text-s"
        affects="bold"
        title={name}
      >
        {name}
      </Typography>
      <Button
        size="small"
        className="px-2 flex-shrink-0"
        variant="transparent"
        type="button"
        onClick={onRemove}
      >
        <TrashIcon className="w-4 h-4" />
      </Button>
    </div>
  );
};
