@use 'styles/shared' as *;

.label {
  @include label-style(sm, $color-primary);

  display: flex;
  box-sizing: border-box;
  align-items: center;
  transform: translateY(to-rem(24px));
  transition: ease 0.23s transform;
  text-transform: uppercase;

  &--topped {
    transform: translateY(0);
  }
}

.input {
  @include label-style(default, $color-black);

  width: 100%;
  border: none;
  border-bottom: to-rem(1px) solid $color-tertiary;
  background: transparent !important;
  padding-bottom: to-rem(8px);

  &:focus-visible {
    outline: none;
    border-color: $color-black;
  }

  &--invalid {
    color: $color-secondary-red;
    -webkit-text-fill-color: $color-secondary-red;
    border-color: $color-secondary-red;

    &::placeholder {
      color: $color-secondary-red;
    }
  }

  &--tooltipped {
    padding-right: to-rem(24px);
  }

  &--disabled {
    color: $color-tertiary-dark;

    &::placeholder {
      color: $color-tertiary-dark;
    }
  }
}

.container {
  position: relative;
}

.input-container {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.tooltip-container {
  position: absolute;
  bottom: to-rem(10px);
  right: 0;
}

.tooltip-icon {
  pointer-events: none;
}

.currency {
  position: absolute;
  bottom: to-rem(12.75px);
  color: $color-tertiary-dark;

  &--invalid {
    color: $color-secondary-red;
  }
}

.secondary {
  .input-container {
    background: $color-secondary-white;
    border-radius: to-rem(100px);
  }

  .currency {
    font-size: $font-size-xl;
    line-height: $line-height-lg-2;
    bottom: to-rem(12px);
    color: $color-primary;

    &--invalid {
      color: $color-secondary-red;
    }
  }

  .input {
    border: to-rem(2px) solid $color-secondary-white;
    box-sizing: border-box;
    padding: 0 to-rem(16px);
    height: to-rem(60px);
    font-size: $font-size-xl;
    line-height: $line-height-lg-2;
    border-radius: inherit;
    text-align: center;
    color: $color-primary;

    &::placeholder {
      color: $color-primary;
    }

    &:hover {
      border-color: $color-grey-blue;
    }

    &:focus {
      border-color: $color-primary;
    }

    &--invalid:focus {
      border-color: $color-secondary-red;
    }
  }
}
