import {
  type Application,
  ApplicationScheduleType,
  type CreditSettingCalculator,
} from 'api/core/generated';
import {
  Currencies,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
} from 'app-constants';
import type { TFunction } from 'i18next';
import {
  PayoutMethod,
  type PayoutMethodConfig,
  type SummaryTableObject,
} from 'models';

import { loadScriptInHead } from './dom-service';

export const getSigningPageRangeValues = (
  minAmount: number,
  maxAmount: number,
) => {
  const rangeValuesArray = [];
  const AMOUNT_RANGE_STEP = 10;

  for (let i = minAmount; i <= maxAmount; i += AMOUNT_RANGE_STEP) {
    const rangeValue = Math.round((i + Number.EPSILON) * 100) / 100;
    rangeValuesArray.push(rangeValue);
  }

  return rangeValuesArray;
};

export const loadAndInitializeIdCardScripts = () => {
  loadScriptInHead(
    `${window.location.origin}/dokobit/applet/jquery-1.11.1.min.js`,
  ).onload = () => {
    loadScriptInHead(
      `${window.location.origin}/dokobit/applet/applet.js`,
    ).onload = () => {
      loadScriptInHead(
        `${window.location.origin}/dokobit/applet/hwcrypto.js`,
      ).onload = () => {
        loadScriptInHead(
          `${window.location.origin}/dokobit/applet/hwcrypto-legacy.js`,
        );
      };
    };
  };
};

export const getSmallLoanSummary = ({
  t,
  application,
}: {
  t: TFunction;
  application: Application;
}): SummaryTableObject[] => {
  const requestedAmount = +application.requested_amount;
  const periodMonths = application.credit_info?.period_months || 0;
  const monthlyPayment = application.credit_info?.monthly_payment;

  const summary = [
    {
      title: t(LOCIZE_COMMON_TRANSLATION_KEYS.loanAmountTitle),
      value: `${requestedAmount} ${Currencies.euro}`,
    },
    {
      title: t(LOCIZE_COMMON_TRANSLATION_KEYS.periodTitle),
      value: `${periodMonths} ${t(LOCIZE_COMMON_TRANSLATION_KEYS.monthsTitle)}`,
    },
    {
      title: t(LOCIZE_COMMON_TRANSLATION_KEYS.monthlyPaymentTitle),
      value: `${monthlyPayment} ${Currencies.euro}`,
    },
  ];

  if (application.credit_info?.fixed_contract_fee === 0) {
    summary.push({
      title: t(LOCIZE_COMMON_TRANSLATION_KEYS.contractFeeTitle),
      value: `0 ${Currencies.euro}`,
    });
  }

  return summary;
};

export const getHirePurchaseSigningPageSummary = ({
  t,
  application,
  currentCreditSetting,
  firstDueAtDate,
}: {
  t: TFunction;
  application: Application;
  currentCreditSetting: Nullable<CreditSettingCalculator>;
  firstDueAtDate: string;
}): SummaryTableObject[] => {
  const fixedContractFee = application.credit_info?.fixed_contract_fee;
  const convertingScheduleFixedContractFee =
    application.credit_info?.converting_schedule_fixed_contract_fee;
  const isRegularScheduleType =
    application.schedule_type === ApplicationScheduleType.REGULAR;
  const monthlyPayment =
    currentCreditSetting?.monthly_payment ??
    application.credit_info?.monthly_payment;

  const periodMonths =
    currentCreditSetting?.month ??
    (application.credit_info?.period_months || 0);

  const shouldShowContractFeeRow =
    (isRegularScheduleType && fixedContractFee === 0) ||
    (!isRegularScheduleType && convertingScheduleFixedContractFee === 0);
  const shouldShowPaymentScheduleRow =
    (application.schedule_type === ApplicationScheduleType.ESTO_X ||
      application.schedule_type === ApplicationScheduleType.PAY_LATER) &&
    !application.free_hp_enabled;

  const contractFeeTableRow = {
    title: t(LOCIZE_COMMON_TRANSLATION_KEYS.contractFeeTitle),
    value: `0 ${Currencies.euro}`,
  };
  const paymentScheduleTableRow = {
    title: t(LOCIZE_COMMON_TRANSLATION_KEYS.paymentScheduleTitle),
    value: `${periodMonths} ${t(
      periodMonths === 1
        ? LOCIZE_COMMON_TRANSLATION_KEYS.monthTitle
        : LOCIZE_COMMON_TRANSLATION_KEYS.monthsTitle,
    )}`,
  };

  return [
    {
      isActive: shouldShowContractFeeRow,
      data: contractFeeTableRow,
    },
    {
      isActive: true,
      data: {
        title: t(LOCIZE_COMMON_TRANSLATION_KEYS.firstPaymentDateTitle),
        value: firstDueAtDate,
      },
    },
    {
      isActive: true,
      data: {
        title: t(LOCIZE_COMMON_TRANSLATION_KEYS.periodTitle),
        value: `${periodMonths}`,
      },
    },
    {
      isActive: shouldShowPaymentScheduleRow,
      data: paymentScheduleTableRow,
    },
    {
      isActive: true,
      data: {
        title: t(LOCIZE_COMMON_TRANSLATION_KEYS.monthlyPaymentTitle),
        value: `${monthlyPayment} ${Currencies.euro}`,
      },
    },
  ]
    .filter(({ isActive }) => Boolean(isActive))
    .map(({ data }) => data);
};

export const getSmallLoanPayoutMethodsConfig = ({
  t,
  instantPayoutFee,
}: {
  t: TFunction;
  instantPayoutFee: number;
}): Array<PayoutMethodConfig> => [
  {
    method: PayoutMethod.Instant,
    title: t(LOCIZE_SIGNING_TRANSLATION_KEYS.instantPayoutMethodTypeTitle),
    description: t(
      LOCIZE_SIGNING_TRANSLATION_KEYS.instantPayoutMethodTypeDescription,
      { fee: instantPayoutFee },
    ),
  },
  {
    method: PayoutMethod.Regular,
    title: t(LOCIZE_SIGNING_TRANSLATION_KEYS.regularPayoutMethodTypeTitle),
    description: t(
      LOCIZE_SIGNING_TRANSLATION_KEYS.regularPayoutMethodTypeDescription,
    ),
  },
];
