import {
  AppLanguages,
  AppRoutePaths,
  LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS,
  LocizeNamespaces,
  REDIRECT_URLS,
} from 'app-constants';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useIncomeInsuranceRootContext } from 'context/income-insurance';
import CloseIcon from 'icons/close-cancel.svg?react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { isAppLanguage } from 'utils';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const REJECTION_REASONS = {
  ageIsOutOfRange: 'INSURANCE_WARNING_USER_AGE_IS_OUT_OF_RANGE',
  unpaidInvoice: 'INSURANCE_WARNING_USER_HAS_UNPAID_INVOICE',
};

const Page = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.incomeInsurance);
  const navigate = useNavigate();

  const { storedInsurance, resetInsuranceInstance } =
    useIncomeInsuranceRootContext();
  const [isNavigating, setIsNavigating] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isSupportRedirecting, setIsSupportRedirecting] = useState(false);

  const onTryAgainButtonClick = () => {
    setIsNavigating(true);
    resetInsuranceInstance();
    navigate(`/${AppRoutePaths.INCOME_INSURANCE}`);
  };

  const onSupportButtonClick = () => {
    setIsSupportRedirecting(true);
    window.location.href =
      REDIRECT_URLS.frequentlyAskedQuestionsPageUrls[
        isAppLanguage(i18n.language) ? i18n.language : AppLanguages.en
      ];
  };

  const renderRejectionDisclaimer = () => {
    switch (storedInsurance?.rejection_reason) {
      case REJECTION_REASONS.ageIsOutOfRange:
        return t(
          LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.rejectReason.ageIsOutOfRange,
        );
      case REJECTION_REASONS.unpaidInvoice:
        return t(
          LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.rejectReason.unpaidInvoice,
        );
      default:
        return t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.reject.disclaimer);
    }
  };

  const onAccountButtonClick = () => {
    setIsRedirecting(true);
    window.location.href = REDIRECT_URLS.incomeInsurance;
  };

  const shouldShowTryAgainButton =
    storedInsurance?.rejection_reason !== REJECTION_REASONS.ageIsOutOfRange &&
    storedInsurance?.rejection_reason !== REJECTION_REASONS.unpaidInvoice;

  return (
    <FullScreenLayout>
      <div className=" flex items-center justify-center rounded-full w-[3.5rem] h-[3.5rem] bg-neutral-200 mb-6">
        <CloseIcon className="rounded-full" />
      </div>
      <Typography variant="xs" className="text-center">
        {t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.reject.title)}
      </Typography>

      <Typography className="mt-4 text-center">
        {renderRejectionDisclaimer()}
      </Typography>

      <div className="mt-12 w-full">
        {shouldShowTryAgainButton ? (
          <Button
            onClick={onTryAgainButtonClick}
            fullWidth
            loading={isNavigating}
          >
            {t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.reject.tryAgain)}
          </Button>
        ) : (
          <Button
            fullWidth
            onClick={onAccountButtonClick}
            loading={isRedirecting}
          >
            {t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.reject.myAccount)}
          </Button>
        )}
        <Button
          onClick={onSupportButtonClick}
          variant="grey"
          fullWidth
          loading={isSupportRedirecting}
          className="mt-4"
        >
          {t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.reject.support)}
        </Button>
      </div>
    </FullScreenLayout>
  );
};

export default Page;
