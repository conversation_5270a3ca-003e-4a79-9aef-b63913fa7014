import { Typography } from '@components/typography';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@components/ui/carousel';
import { Skeleton } from '@components/ui/skeleton';
import { useGetDashboardSubscriptionsCarouselBanners } from '@pages/dashboard/hooks/useGetDashboardSubscriptionsCarouselBanners';
import Autoplay from 'embla-carousel-autoplay';
import Fade from 'embla-carousel-fade';
import { useState } from 'react';

import { DashboardSubscriptionsCarouselButton } from './DashboardSubscriptionsCarouselButton';

export const DashboardSubscriptionsCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();

  const banners = useGetDashboardSubscriptionsCarouselBanners();

  if (banners.length === 0) return null;

  return (
    <Carousel
      className="flex h-[14rem] px-6 mb-12 md:m-0 md:p-0 min-w-full"
      opts={{
        loop: true,
        duration: 30,
      }}
      setApi={setApi}
      onMouseLeave={() => api?.plugins().autoplay.play()}
      plugins={[
        Autoplay({
          delay: 5000,
          stopOnMouseEnter: true,
          playOnInit: true,
          stopOnInteraction: true,
          // added to fix onMouseEnter and onMouseLeave events
          rootNode: (emblaRoot) => emblaRoot.parentElement,
        }),
        Fade(),
      ]}
    >
      <div className="relative h-full w-full">
        <CarouselContent isInteractive>
          {banners.map((banner) => (
            <CarouselItem key={banner.id}>
              <div
                className="flex items-center h-full w-full rounded-2xl cursor-pointer bg-center bg-cover"
                key={banner.id}
                style={{
                  backgroundImage: banner.img
                    ? `url(${banner.img})`
                    : undefined,
                }}
              >
                <div className="flex flex-col justify-center p-6 gap-4">
                  <Typography variant="xxs" className="text-primary-white">
                    {banner.title}
                  </Typography>
                  <Typography
                    variant="text-s"
                    affects="semibold"
                    className="text-primary-white"
                  >
                    {banner.description}
                  </Typography>

                  <DashboardSubscriptionsCarouselButton id={banner.id} />
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        {banners.length > 1 && (
          <>
            <CarouselPrevious className="-left-4 absolute p-1 border border-neutral-100" />
            <CarouselNext className="-right-4 absolute p-1 border border-neutral-100" />
          </>
        )}
      </div>
    </Carousel>
  );
};

export const DashboardSubscriptionsCarouselSkeleton = () => (
  <Skeleton className="flex h-[14rem] px-6 mb-12 md:m-0 md:p-0" />
);
