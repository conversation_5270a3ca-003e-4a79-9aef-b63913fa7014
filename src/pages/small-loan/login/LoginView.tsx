import {
  APP_DISCLAIMER_TYPES,
  AppLoginMethods,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { AppButton, AppDisclaimer } from 'components';
import { useLoginPageContext } from 'context/small-loan';
import { useTranslation } from 'react-i18next';

import styles from './Login.module.scss';
import { LOGIN_FORMS_MAP } from './login-forms.map';

export const LoginView = () => {
  const {
    loginMethodButtons,
    selectedLoginMethod,
    isLoginFlowWithError,
    visiblePageAttributes,
  } = useLoginPageContext();

  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: te } = useTranslation(LocizeNamespaces.errors);

  const loginPageHeading = t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginPageHeading);
  const errorTitle = te(
    LOCIZE_ERRORS_TRANSLATION_KEYS.authenticationErrorLabel,
  );

  const renderLoginMethodButtons = () =>
    loginMethodButtons?.map(({ ...props }) => (
      <AppButton
        {...props}
        className={styles['login-button']}
        key={props.key}
      />
    ));

  const LoginForm = LOGIN_FORMS_MAP[selectedLoginMethod];

  return (
    <>
      <p className={styles.heading}>{loginPageHeading}</p>
      <div className={styles.buttons}>{renderLoginMethodButtons()}</div>

      <AppDisclaimer
        title={t(LOCIZE_LOGIN_TRANSLATION_KEYS.smartIdFullVersionDisclaimer)}
        type={APP_DISCLAIMER_TYPES.warning}
        visible={
          visiblePageAttributes[PageAttributeNames.smartIdFullDisclaimer] &&
          selectedLoginMethod === AppLoginMethods.smartId
        }
      />

      <AppDisclaimer
        title={errorTitle}
        type={APP_DISCLAIMER_TYPES.error}
        visible={isLoginFlowWithError}
      />

      <LoginForm />
    </>
  );
};
