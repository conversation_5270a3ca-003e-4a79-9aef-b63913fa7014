import {
  APP_DISCLAIMER_TYPES,
  AppLoginMethods,
  AppPinTypes,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { AppDisclaimer, AppPinConfirmationComponent } from 'components';
import { useLoginPageContext } from 'context/small-loan';
import { useTranslation } from 'react-i18next';

export const PinConfirmationView = () => {
  const {
    smartIdChallengeId,
    mobileIdChallengeId,
    stopLoginPolling,
    selectedLoginMethod,
    visiblePageAttributes,
  } = useLoginPageContext();

  const { t } = useTranslation(LocizeNamespaces.login);

  return (
    <>
      <AppPinConfirmationComponent
        onCancel={stopLoginPolling}
        pin={smartIdChallengeId || mobileIdChallengeId}
        pinType={AppPinTypes.pin1}
      />

      <AppDisclaimer
        title={t(LOCIZE_LOGIN_TRANSLATION_KEYS.smartIdFullVersionDisclaimer)}
        type={APP_DISCLAIMER_TYPES.warning}
        visible={
          visiblePageAttributes[PageAttributeNames.smartIdFullDisclaimer] &&
          selectedLoginMethod === AppLoginMethods.smartId
        }
      />
    </>
  );
};
