import {
  AppLoginMethods,
  AppPinTypes,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { AppPinConfirmationComponent } from 'components';
import { DisclaimerNotification } from 'components/notification';
import { useLoginPageContext } from 'context/small-loan';
import { useTranslation } from 'react-i18next';

export const PinConfirmationView = () => {
  const {
    smartIdChallengeId,
    mobileIdChallengeId,
    stopLoginPolling,
    selectedLoginMethod,
    visiblePageAttributes,
  } = useLoginPageContext();

  const { t } = useTranslation(LocizeNamespaces.login);

  return (
    <>
      <AppPinConfirmationComponent
        onCancel={stopLoginPolling}
        pin={smartIdChallengeId || mobileIdChallengeId}
        pinType={AppPinTypes.pin1}
      />

      {visiblePageAttributes[PageAttributeNames.smartIdFullDisclaimer] &&
        selectedLoginMethod === AppLoginMethods.smartId && (
          <DisclaimerNotification className="mt-6">
            {t(LOCIZE_LOGIN_TRANSLATION_KEYS.smartIdFullVersionDisclaimer)}
          </DisclaimerNotification>
        )}
    </>
  );
};
