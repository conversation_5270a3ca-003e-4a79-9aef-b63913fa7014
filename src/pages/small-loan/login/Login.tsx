import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LoginPageViewTypes,
} from 'app-constants';
import { AppContactUs, AppLoader, AppLoaderContentWrapper } from 'components';
import { useLoginPageContext } from 'context/small-loan';
import { LoginPageLogicProvider } from 'context/small-loan/LoginPageLogicProvider';

import { LoginView } from './LoginView';
import { MagicLinkView } from './MagicLinkView';
import { PendingView } from './PendingView';
import { PinConfirmationView } from './PinConfirmationView';

const Page = () => {
  const { loginPageLoaded, processingLoginPage, pageViewType } =
    useLoginPageContext();

  if (!loginPageLoaded) {
    return <AppLoader isRelative />;
  }

  const renderLoginPageContent = () => {
    switch (pageViewType) {
      case LoginPageViewTypes.pinConfirmation:
        return <PinConfirmationView />;
      case LoginPageViewTypes.magic:
        return <MagicLinkView />;
      case LoginPageViewTypes.pending:
        return <PendingView />;
      default:
        return <LoginView />;
    }
  };

  return (
    <AppLoaderContentWrapper shouldShowLoader={processingLoginPage}>
      {renderLoginPageContent()}
      <AppContactUs
        text={LOCIZE_COMMON_TRANSLATION_KEYS.contactInfoDisclaimer}
        visible={pageViewType === LoginPageViewTypes.login}
      />
    </AppLoaderContentWrapper>
  );
};

const LoginPage = () => (
  <LoginPageLogicProvider>
    <Page />
  </LoginPageLogicProvider>
);

export default LoginPage;
