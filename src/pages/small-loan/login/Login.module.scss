@use 'styles/shared' as *;

.buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0 to-rem(16px);
  margin-top: to-rem(40px);
}

.form {
  margin-top: to-rem(30px);

  .banklink-loader {
    position: relative;
    justify-content: center;
    margin-top: to-rem(32px);
  }

  .submit-button {
    margin: to-rem(40px) auto;
    width: 100%;
  }
}

.id-card-disclaimer {
  @include paragraph-style(default, $color-black);

  margin-top: to-rem(30px);
  text-align: center;
}

.paragraph {
  @include paragraph-style(default, $color-black);

  text-align: center;

  &:first-of-type {
    margin-top: to-rem(40px);
  }
}

.heading {
  @include paragraph-style(xl, $color-primary);

  margin: to-rem(40px) auto 0;
  text-align: center;
}

.magic-link-view-container,
.pending-view-container {
  .paragraph {
    margin-top: to-rem(30px);
  }
}

@include media(mobile) {
  .buttons {
    .login-button {
      max-width: unset;
      min-width: unset;
    }
  }
}
