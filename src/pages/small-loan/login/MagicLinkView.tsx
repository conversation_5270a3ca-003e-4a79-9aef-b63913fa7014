import {
  AppFormFieldTypes,
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  LoginPageViewTypes,
  STEP_COUNTER_COMPONENT_ID,
} from 'app-constants';
import { AppForm, AppFormButton, AppFormField } from 'components';
import { useLoginPageContext } from 'context/small-loan';
import { usePayseraEmailLogin } from 'hooks';
import { DisplayStyles } from 'models';
import { useLayoutEffect } from 'react';
import type { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { extractValidationErrors, setElementDisplayById } from 'services';

import styles from './Login.module.scss';

export const MagicLinkView = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: tr } = useTranslation(LocizeNamespaces.common);

  const { setPageViewType } = useLoginPageContext();

  const {
    payseraEmailLogin,
    payseraEmailLoginProcessing,
    payseraEmailLoginError,
  } = usePayseraEmailLogin();

  const loginValidationErrors = extractValidationErrors(payseraEmailLoginError);

  useLayoutEffect(() => {
    setElementDisplayById(STEP_COUNTER_COMPONENT_ID, DisplayStyles.none);
    return () => {
      setElementDisplayById(STEP_COUNTER_COMPONENT_ID, DisplayStyles.flex);
    };
  });

  const onMagicLinkFormSubmit = ({ email }: FieldValues) => {
    payseraEmailLogin(email).then((response) => {
      if (response.data?.success) {
        setPageViewType(LoginPageViewTypes.pending);
      }
    });
  };

  return (
    <div className={styles['magic-link-view-container']}>
      <p className={styles.paragraph}>
        {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginConfirmationProcessingDisclaimer)}
      </p>
      <p className={styles.paragraph}>
        {t(
          LOCIZE_LOGIN_TRANSLATION_KEYS.customerAwaitingEmailAddressDisclaimer,
        )}
      </p>
      <AppForm className={styles.form} onSubmit={onMagicLinkFormSubmit}>
        <AppFormField
          invalid={loginValidationErrors[FormFieldNames.email]}
          label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.emailFieldLabel)}
          name={FormFieldNames.email}
          type={AppFormFieldTypes.email}
          visible
        />
        <AppFormButton
          className={styles['submit-button']}
          disablingEmptyFieldNames={[FormFieldNames.email]}
          isDisabled={payseraEmailLoginProcessing}
          isSubmit
          label={tr(LOCIZE_COMMON_TRANSLATION_KEYS.submit)}
        />
      </AppForm>
      <p className={styles.paragraph}>
        {t(LOCIZE_LOGIN_TRANSLATION_KEYS.customerAwaitingPatienceDisclaimer)}
      </p>
    </div>
  );
};
