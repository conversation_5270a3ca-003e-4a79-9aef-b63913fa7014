import {
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import c from 'clsx';
import FastLoanHeaderImg from 'img/consumer-loan-checkout-headers/fast-loan-checkout-header.webp';
import RenovationLoanHeaderImg from 'img/consumer-loan-checkout-headers/renovation-loan-checkout-header.webp';
import SmallLoanHeaderImg from 'img/consumer-loan-checkout-headers/small-loan-checkout-header.webp';
import VehicleLoanHeaderImg from 'img/consumer-loan-checkout-headers/vehicle-loan-checkout-header.webp';
import { ConsumerLoanProduct } from 'models';
import { useTranslation } from 'react-i18next';

import styles from './Checkout.module.scss';

type CheckoutHeaderProps = {
  scheduleType: ConsumerLoanProduct;
};

type CheckoutHeaderConfigType = {
  title: string;
  subtitle: string;
  img: string;
};

export const CheckoutHeader = ({ scheduleType }: CheckoutHeaderProps) => {
  const { t } = useTranslation(LocizeNamespaces.checkout);

  const checkoutHeaderConfig: CheckoutHeaderConfigType = {
    [ConsumerLoanProduct.SMALL_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMarketingTitle),
      subtitle: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMarketingSubtitle),
      img: SmallLoanHeaderImg,
    },
    [ConsumerLoanProduct.FAST_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.fastLoanMarketingTitle),
      subtitle: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.fastLoanMarketingSubtitle),
      img: FastLoanHeaderImg,
    },
    [ConsumerLoanProduct.VEHICLE_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.vehicleLoanMarketingTitle),
      subtitle: t(
        LOCIZE_CHECKOUT_TRANSLATION_KEYS.vehicleLoanMarketingSubtitle,
      ),
      img: VehicleLoanHeaderImg,
    },
    [ConsumerLoanProduct.RENOVATION_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.renovationLoanMarketingTitle),
      subtitle: t(
        LOCIZE_CHECKOUT_TRANSLATION_KEYS.renovationLoanMarketingSubtitle,
      ),
      img: RenovationLoanHeaderImg,
    },
    [ConsumerLoanProduct.TRAVEL_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.travelLoanMarketingTitle),
      subtitle: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.travelLoanMarketingSubtitle),
      img: SmallLoanHeaderImg,
    },
    [ConsumerLoanProduct.HEALTH_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.healthLoanMarketingTitle),
      subtitle: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.healthLoanMarketingSubtitle),
      img: SmallLoanHeaderImg,
    },
    [ConsumerLoanProduct.BEAUTY_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.beautyLoanMarketingTitle),
      subtitle: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.beautyLoanMarketingSubtitle),
      img: SmallLoanHeaderImg,
    },
  }[scheduleType];

  return (
    <div className={styles.small__loan__marketing}>
      <img
        alt={checkoutHeaderConfig.img}
        className={styles.small__loan__marketing__img}
        src={checkoutHeaderConfig.img}
      />
      <div className={styles.small__loan__marketing__overlay} />
      <div className={styles.small__loan__marketing__text}>
        {checkoutHeaderConfig.title}
      </div>
      <div
        className={c(
          styles.small__loan__marketing__text,
          styles.small__loan__marketing__text__bottom,
        )}
      >
        {checkoutHeaderConfig.subtitle}
      </div>
    </div>
  );
};
