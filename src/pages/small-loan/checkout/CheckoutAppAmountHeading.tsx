import { FormFieldNames } from 'app-constants';
import { AppAmountHeading } from 'components';
import type { ConsumerLoanProduct } from 'models';
import { useFormContext } from 'react-hook-form';
import { calculateMonthlyPayment } from 'services';

type CheckoutAppAmountHeadingProps = {
  title: string;
  applicationScheduleType: ConsumerLoanProduct;
};

export const CheckoutAppAmountHeading = ({
  title,
  applicationScheduleType,
}: CheckoutAppAmountHeadingProps) => {
  const { watch } = useFormContext();
  const periodValue = watch(FormFieldNames.periodMonths);
  const loanAmountValue = watch(FormFieldNames.netTotal);

  const monthlyPayment = calculateMonthlyPayment(
    loanAmountValue,
    periodValue,
    applicationScheduleType,
  );

  return <AppAmountHeading title={title} value={monthlyPayment} visible />;
};
