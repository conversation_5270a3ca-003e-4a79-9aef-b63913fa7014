import {
  FormFieldNames,
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { FormPeriodField } from 'components/form/form-period-field/FormPeriodField';
import { FormRangeInputField } from 'components/form/form-range-input-field/FormRangeInputField';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useRootContext } from 'context/root';
import { useCheckoutPageLogic } from 'hooks/page-logic/small-loan';
import { useIsMobileView } from 'hooks/system';
import { useTranslation } from 'react-i18next';
import { getConsumerLoanProductByScheduleType } from 'utils';

import { SmallLoanTitle } from '../root/ui/SmallLoanTitle';
import CheckoutAmountHeading from './ChekoutAmountHeading';

const STEP = 10;

const Checkout = () => {
  const { user } = useRootContext();

  const {
    form,
    onCheckoutPageFormSubmit,
    rangeValues,
    onLoanAmountChange,
    application,
    periodValues,
    onPeriodChange,
    nextButtonIsDisabled,
  } = useCheckoutPageLogic();

  form.watch(FormFieldNames.periodMonths);

  const { t } = useTranslation(LocizeNamespaces.checkout);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const consumerLoanProduct = getConsumerLoanProductByScheduleType(
    application?.schedule_type,
  );

  const isMobileView = useIsMobileView();

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onCheckoutPageFormSubmit)}
        className="w-full"
      >
        {!isMobileView && <SmallLoanTitle className="text-[1.875rem] mb-4" />}

        <div className={'mb-10'}>
          <FormRangeInputField
            onAmountChange={onLoanAmountChange}
            control={form.control}
            name={FormFieldNames.netTotal}
            min={+rangeValues[0]}
            max={+rangeValues[rangeValues.length - 1]}
            step={STEP}
            disabled={form.formState.isSubmitting}
            size={isMobileView ? 'regular' : 'small'}
          />
        </div>

        <div className={'mt-4'}>
          <FormPeriodField
            className={'mt-4'}
            control={form.control}
            label={'Period'}
            name={FormFieldNames.periodMonths}
            periodValues={periodValues}
            onPeriodChange={onPeriodChange}
            disabled={form.formState.isSubmitting}
            size={isMobileView ? 'regular' : 'small'}
          />
        </div>

        <CheckoutAmountHeading
          className="mt-10"
          title={t(
            LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMonthlyPaymentTitle,
          )}
          applicationScheduleType={consumerLoanProduct}
          description={t(
            LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMonthlyPaymentDescription,
          )}
        />

        <Button
          className="mt-10"
          type="submit"
          fullWidth
          loading={form.formState.isSubmitting}
          disabled={nextButtonIsDisabled || !form.formState.isValid}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>

        {!user ? (
          <div>
            {t(
              LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMonthlyPaymentDisclaimer,
            )}
          </div>
        ) : null}
      </form>
    </Form>
  );
};

export default Checkout;
