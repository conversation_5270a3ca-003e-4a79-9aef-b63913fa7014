import {
  AppFormFieldTypes,
  Form<PERSON>ieldNames,
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  SMALL_LOAN_START_ID,
  SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN,
} from 'app-constants';
import c from 'clsx';
import {
  AppAmountField,
  AppBold,
  AppForm,
  AppFormButton,
  AppFormField,
  AppLoader,
  AppLoaderContentWrapper,
  AppLocalizationComponent,
  AppPaymentLeaveToggler,
  AppSpecialOffer,
} from 'components';
import { useRootContext } from 'context/root';
import { useCheckoutPageLogic } from 'hooks/page-logic/small-loan';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getConsumerLoanProductByScheduleType } from 'utils';

import styles from './Checkout.module.scss';
import { CheckoutAppAmountHeading } from './CheckoutAppAmountHeading';
import { CheckoutHeader } from './CheckoutHeader';

const Checkout = () => {
  const { applicationPrivateInfo, user } = useRootContext();

  const {
    checkoutPageFormConfig,
    onCheckoutPageFormSubmit,
    processingCheckout,
    checkoutPageLoaded,
    periodValues,
    visiblePageAttributes,
    nextButtonIsDisabled,
    rangeValues,
    application,
    onLoanAmountChange,
    onTogglePaymentLeaveOffer,
    isPaymentLeaveEnabled,
    debouncedPeriodChange,
  } = useCheckoutPageLogic();

  const { t } = useTranslation(LocizeNamespaces.checkout);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const paymentLeaveMonths = application?.campaign?.payment_leave_months ?? 0;
  const interestFreeMonths = application?.campaign?.interest_free_months ?? 0;
  const consumerLoanProduct = getConsumerLoanProductByScheduleType(
    application?.schedule_type,
  );

  const specialOfferLocizeKey = useMemo(() => {
    if (
      visiblePageAttributes[
        PageAttributeNames.specialOfferContainerInterestFreeDisclaimer
      ]
    ) {
      if (interestFreeMonths <= 1) {
        return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_1_MONTH;
      }

      return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_MANY_MONTHS;
    }

    if (
      visiblePageAttributes[
        PageAttributeNames.fixedZeroContractFeeDisclaimer
      ] &&
      visiblePageAttributes[PageAttributeNames.fixedZeroManagementFeeDisclaimer]
    ) {
      return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.VARIANT_1;
    }

    if (
      visiblePageAttributes[
        PageAttributeNames.fixedZeroContractFeeDisclaimer
      ] ||
      visiblePageAttributes[PageAttributeNames.fixedZeroManagementFeeDisclaimer]
    ) {
      return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.DEFAULT;
    }

    return null;
  }, [visiblePageAttributes, interestFreeMonths]);

  if (!checkoutPageLoaded) {
    return <AppLoader isRelative />;
  }

  return (
    <AppLoaderContentWrapper shouldShowLoader={processingCheckout}>
      <AppForm
        className={c(styles.form, {
          [styles['form--test-mode']]: Boolean(applicationPrivateInfo?.is_test),
          [styles.display__none]: processingCheckout,
        })}
        formConfig={checkoutPageFormConfig}
        onSubmit={onCheckoutPageFormSubmit}
      >
        <CheckoutHeader scheduleType={consumerLoanProduct} />

        <AppAmountField
          isSmallLoan
          onAmountChange={onLoanAmountChange}
          rangeValues={rangeValues}
          visible
        />

        {periodValues.length > 1 ? (
          <AppFormField
            onlySliderAndSelect
            label={tc(LOCIZE_COMMON_TRANSLATION_KEYS.periodTitle)}
            name={FormFieldNames.periodMonths}
            onChange={debouncedPeriodChange}
            periodValues={periodValues}
            rangeLabel={t(LOCIZE_COMMON_TRANSLATION_KEYS.monthsTitle)}
            showMinMaxLabelsOnly
            type={AppFormFieldTypes.period}
            visible={visiblePageAttributes[PageAttributeNames.periodSelector]}
          />
        ) : null}

        {specialOfferLocizeKey ? (
          <AppSpecialOffer
            description={
              <AppLocalizationComponent
                components={{
                  make_bold: <AppBold />,
                }}
                locizeKey={specialOfferLocizeKey}
                t={t}
                values={{
                  numberOfMonths: interestFreeMonths,
                }}
              />
            }
            visible
          />
        ) : null}

        <AppPaymentLeaveToggler
          className={styles['payment-leave-toggler']}
          description={tc(
            LOCIZE_COMMON_TRANSLATION_KEYS.specialOfferPaymentLeaveSmallLoanDescription,
            {
              numberOfMonths: `${paymentLeaveMonths + 1} ${tc(
                paymentLeaveMonths + 1 === 1
                  ? LOCIZE_COMMON_TRANSLATION_KEYS.monthTitle
                  : LOCIZE_COMMON_TRANSLATION_KEYS.monthsTitle,
              )}`,
              merchantName: '',
            },
          )}
          isToggled={isPaymentLeaveEnabled}
          setIsToggled={onTogglePaymentLeaveOffer}
          title={tc(
            LOCIZE_COMMON_TRANSLATION_KEYS.specialOfferPaymentLeaveTitle,
          )}
          visible={
            paymentLeaveMonths > 0 &&
            visiblePageAttributes[
              PageAttributeNames.radioButtonContainerStartPaymentAfterMonths
            ]
          }
        />

        <CheckoutAppAmountHeading
          title={tc(LOCIZE_COMMON_TRANSLATION_KEYS.yourMonthlyPaymentTitle)}
          applicationScheduleType={consumerLoanProduct}
        />

        <AppFormButton
          className={c(
            styles['submit-button'],
            styles['submit-button--small-loan'],
          )}
          id={SMALL_LOAN_START_ID}
          isDisabled={nextButtonIsDisabled}
          isSubmit
          label={tc(
            user
              ? LOCIZE_COMMON_TRANSLATION_KEYS.continue
              : LOCIZE_COMMON_TRANSLATION_KEYS.loginToContinue,
          )}
        />

        {!user ? (
          <div className={styles['monthly-payment']}>
            {t(
              LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMonthlyPaymentDisclaimer,
            )}
          </div>
        ) : null}
      </AppForm>
    </AppLoaderContentWrapper>
  );
};

export default Checkout;
