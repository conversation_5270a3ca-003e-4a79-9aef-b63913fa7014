import {
  Currencies,
  DEFAULT_DIGITS_AFTER_DECIMAL_POINT,
  FormFieldNames,
} from 'app-constants';
import { Typography } from 'components/typography';
import type { ConsumerLoanProduct } from 'models';
import { useFormContext } from 'react-hook-form';
import Skeleton from 'react-loading-skeleton';
import { calculateMonthlyPayment } from 'services';
import { cn } from 'utils/tailwind';

type CheckoutAmountHeadingProps = {
  title: string;
  description: string;
  applicationScheduleType: ConsumerLoanProduct;
  className?: string;
  loading?: boolean;
};

const CheckoutAmountHeading = ({
  title,
  description,
  applicationScheduleType,
  className,
  loading,
}: CheckoutAmountHeadingProps) => {
  const { watch } = useFormContext();
  const periodValue = watch(FormFieldNames.periodMonths);
  const loanAmountValue = watch(FormFieldNames.netTotal);

  const monthlyPayment = calculateMonthlyPayment(
    loanAmountValue,
    periodValue,
    applicationScheduleType,
  );
  return (
    <div className={cn('flex flex-col gap-4', className)}>
      <div className="flex flex-row justify-between items-baseline">
        <Typography variant="text-l">{title}</Typography>
        <span className="font-bold text-[1.875rem]">
          {loading ? (
            <Skeleton className="rounded-3xl !w-[10rem] h-7.5 animate-pulse-opacity" />
          ) : (
            `${monthlyPayment.toFixed(DEFAULT_DIGITS_AFTER_DECIMAL_POINT)} ${Currencies.euro}`
          )}
        </span>
      </div>

      <Typography variant="text-s" className="text-neutral-500">
        {description}
      </Typography>
    </div>
  );
};

export default CheckoutAmountHeading;
