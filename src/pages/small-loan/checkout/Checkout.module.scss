@use 'styles/shared' as *;

.form {
  color: $color-secondary;

  &--test-mode {
    margin-top: to-rem(40px);
  }

  .submit-button {
    margin: to-rem(40px) auto 0;
    width: 100%;
  }
}

.table {
  margin-top: to-rem(40px);
}

.small__loan__marketing {
  position: relative;
  padding: to-rem(20px);
  display: flex;
  flex-direction: column;
  justify-content: center;

  &__text {
    position: relative;
    font-weight: $font-weight-regular;
    font-size: to-rem(48px);
    line-height: to-rem(64px);
    color: $color-black;
    text-align: center;

    &__bottom {
      margin-top: to-rem(10px);
      @include paragraph-style(default);
    }
  }

  &__img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: to-rem(8px);
    object-fit: cover;
    object-position: center;
  }

  &__overlay {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: $color-white;
    opacity: 0.75;
    border-radius: to-rem(8px);
  }
}

.submit-button {
  &--small-loan {
    width: 100%;
  }
}

.monthly-payment {
  @include label-style(default);

  margin-top: to-rem(30px);
  text-align: center;
}

.heading {
  @include heading-style(md, $color-primary);

  text-align: center;
}

.paragraph {
  @include paragraph-style;

  margin-top: to-rem(30px);
  text-align: center;

  &--margin-top-20 {
    margin-top: to-rem(20px);
  }
}

.payment-leave-toggler {
  margin-top: to-rem(40px);
  padding: to-rem(20px);
  border: to-rem(2px) solid $color-secondary-white;
  border-radius: to-rem(16px);

  label {
    align-items: unset;

    > div {
      margin-top: 0;
      width: 85%;
      padding: unset;
    }
  }
}

@include media(mobile) {
  .monthly-payment {
    @include label-style(sm);
  }
}

@include media(up-to-tablet) {
  .payment-leave-toggler {
    padding: unset;
    border: unset;

    label {
      padding: unset;
    }
  }

  .small__loan__marketing {
    padding: to-rem(16px);

    &__text {
      font-size: to-rem(32px);
      line-height: to-rem(32px);

      &__bottom {
        font-size: to-rem(16px);
        line-height: to-rem(24px);
      }
    }

    &__overlay {
      opacity: 0.85;
    }

    &__bottom {
      margin-top: to-rem(16px);
    }
  }
}
