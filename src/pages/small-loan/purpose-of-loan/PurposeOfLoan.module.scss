@use 'styles/shared' as *;

.container {
  .title {
    @include heading-style(md, $color-primary);

    text-align: center;
  }

  .grid {
    margin-top: to-rem(40px);
    display: grid;
    justify-items: center;
    grid-template-columns: repeat(auto-fit, minmax(290px, 1fr));
    grid-gap: to-rem(20px);
  }

  .icon {
    pointer-events: none;
  }

  .card__title {
    @include heading-style(sm, $color-black);
  }

  .card__description {
    @include paragraph-style(default, $color-black);
  }

  .card__title,
  .card__description {
    margin-top: to-rem(15px);
    text-align: center;
    letter-spacing: to-rem(-0.4px);
  }

  .button {
    margin-top: to-rem(40px);
    width: 100%;
  }
}
