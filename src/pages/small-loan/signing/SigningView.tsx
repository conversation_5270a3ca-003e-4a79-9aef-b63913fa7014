import { UsersignInMethod } from 'api/core/generated';
import {
  APP_DISCLAIMER_TYPES,
  AppFormFieldTypes,
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
  LogoutButtonVariants,
  PageAttributeNames,
  PRICING_KEYS,
  REDIRECT_URLS,
  SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN,
} from 'app-constants';
import c from 'clsx';
import {
  AppBold,
  AppDisclaimer,
  AppExternalLink,
  AppForm,
  AppFormButton,
  AppFormField,
  AppLocalizationComponent,
  AppLogOutButton,
  AppPaymentLeaveToggler,
  AppSpecialOffer,
  AppSummaryTable,
} from 'components';
import { useSigningPageContext } from 'context/small-loan';
import { onlyPasswordSigningEnabled } from 'environment';
import { type AnyObject, PayoutMethod } from 'models';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getSmallLoanPayoutMethodsConfig, getSmallLoanSummary } from 'services';

import { PayoutMethodsCheckTabs } from './PayoutMethodsCheckTabs';
import styles from './Signing.module.scss';

export const SigningView = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { t: tch } = useTranslation(LocizeNamespaces.checkout);

  const {
    onSigningFormSubmit,
    signingPageFormConfig,
    visiblePageAttributes,
    contractLink,
    userCanSignContract,
    banklinkOptions,
    isBanklinkOptionSelected,
    selectBanklinkOption,
    processingSigningPage,
    onLogOutClick,
    applicationCampaignUpdating,
    isApplicationPayoutMethodUpdating,
    isInitialLoanAmountDisallowed,
    onTogglePaymentLeaveOffer,
    isPaymentLeaveEnabled,
    application,
    onPaymentMethodTabChecked,
    pricingDataWithKeys,
    signInMethod,
    userInfoValidationErrors,
  } = useSigningPageContext();

  const isBanklinkSigningAllowed =
    !onlyPasswordSigningEnabled &&
    (signInMethod === UsersignInMethod.PAYSERA_BANKLINK ||
      signInMethod === UsersignInMethod.MAGIC_LINK);

  const paymentLeaveMonths = application?.campaign?.payment_leave_months ?? 0;
  const interestFreeMonths = application?.campaign?.interest_free_months ?? 0;

  const specialOfferLocizeKey = useMemo(() => {
    if (
      visiblePageAttributes[
        PageAttributeNames.specialOfferContainerInterestFreeDisclaimer
      ]
    ) {
      if (interestFreeMonths <= 1) {
        return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_1_MONTH;
      }

      return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_MANY_MONTHS;
    }

    if (
      visiblePageAttributes[
        PageAttributeNames.fixedZeroContractFeeDisclaimer
      ] &&
      visiblePageAttributes[PageAttributeNames.fixedZeroManagementFeeDisclaimer]
    ) {
      return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.VARIANT_1;
    }

    if (
      visiblePageAttributes[
        PageAttributeNames.fixedZeroContractFeeDisclaimer
      ] ||
      visiblePageAttributes[PageAttributeNames.fixedZeroManagementFeeDisclaimer]
    ) {
      return SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.DEFAULT;
    }

    return null;
  }, [visiblePageAttributes, interestFreeMonths]);

  const smallLoanSummary = useMemo(
    () =>
      getSmallLoanSummary({
        t: tc,
        application,
      }),
    [tc, application],
  );

  const smallLoanPayoutMethodConfig = useMemo(
    () =>
      getSmallLoanPayoutMethodsConfig({
        t,
        instantPayoutFee:
          pricingDataWithKeys[PRICING_KEYS.smallLoanInstantPayoutFee],
      }),
    [t, pricingDataWithKeys[PRICING_KEYS.smallLoanInstantPayoutFee]],
  );

  return (
    <AppForm
      className={styles.form}
      formConfig={signingPageFormConfig}
      onSubmit={onSigningFormSubmit}
    >
      <p className={styles.heading}>
        {t(LOCIZE_SIGNING_TRANSLATION_KEYS.signingPageHeading)}
      </p>

      {application.is_instant_payout_available &&
      pricingDataWithKeys[PRICING_KEYS.smallLoanInstantPayoutFee] > 0 ? (
        <PayoutMethodsCheckTabs
          defaultSelectedMethod={
            application.is_instant_payout
              ? PayoutMethod.Instant
              : PayoutMethod.Regular
          }
          onPaymentMethodTabChecked={onPaymentMethodTabChecked}
          payoutMethodConfig={smallLoanPayoutMethodConfig}
        />
      ) : null}

      {specialOfferLocizeKey && (
        <AppSpecialOffer
          visible
          description={
            <AppLocalizationComponent
              locizeKey={specialOfferLocizeKey}
              components={{
                make_bold: <AppBold />,
              }}
              values={{
                numberOfMonths: interestFreeMonths,
              }}
              t={tch}
            />
          }
        />
      )}

      <AppPaymentLeaveToggler
        className={styles['payment-leave-toggler']}
        description={tc(
          LOCIZE_COMMON_TRANSLATION_KEYS.specialOfferPaymentLeaveSmallLoanDescription,
          {
            numberOfMonths: `${paymentLeaveMonths + 1} ${tc(
              paymentLeaveMonths + 1 === 1
                ? LOCIZE_COMMON_TRANSLATION_KEYS.monthTitle
                : LOCIZE_COMMON_TRANSLATION_KEYS.monthsTitle,
            )}`,
            merchantName: '',
          },
        )}
        isToggled={isPaymentLeaveEnabled}
        setIsToggled={onTogglePaymentLeaveOffer}
        title={tc(LOCIZE_COMMON_TRANSLATION_KEYS.specialOfferPaymentLeaveTitle)}
        visible={
          paymentLeaveMonths > 0 &&
          visiblePageAttributes[
            PageAttributeNames.radioButtonContainerStartPaymentAfterMonths
          ]
        }
      />

      <AppDisclaimer
        title={t(LOCIZE_SIGNING_TRANSLATION_KEYS.moreThanMaxLoanDisclaimer)}
        type={APP_DISCLAIMER_TYPES.warning}
        visible={isInitialLoanAmountDisallowed}
      />

      <AppSummaryTable visible tableConfig={smallLoanSummary} />

      <AppFormField
        invalid={userInfoValidationErrors[FormFieldNames.iban]}
        className={styles['iban-field']}
        label={t(LOCIZE_SIGNING_TRANSLATION_KEYS.ibanFieldLabel)}
        name={FormFieldNames.iban}
        tooltipLabel={t(LOCIZE_SIGNING_TRANSLATION_KEYS.ibanTooltipLabel)}
        type={AppFormFieldTypes.text}
        visible
        withTooltip
      />

      {userCanSignContract ? (
        <div className={styles['signing-disclaimer']}>
          <AppLocalizationComponent
            components={{
              site_link: (
                <AppExternalLink
                  className={styles['terms-label-link']}
                  to={(REDIRECT_URLS.termsPageUrs as AnyObject)[i18n.language]}
                />
              ),
              contract_link: (
                <AppExternalLink
                  openInNewTab={false}
                  to={contractLink}
                  className={c({
                    [styles.disabled]: isApplicationPayoutMethodUpdating,
                    [styles['terms-label-link']]: true,
                  })}
                />
              ),
            }}
            locizeKey={LOCIZE_SIGNING_TRANSLATION_KEYS.acceptTermsOfContract}
            t={t}
          />
        </div>
      ) : null}

      <AppFormField
        visible={isBanklinkSigningAllowed && !!banklinkOptions?.length}
        banklinkOptions={banklinkOptions}
        type={AppFormFieldTypes.banklink}
        name={FormFieldNames.paymentMethodKey}
        onChange={selectBanklinkOption}
      />

      {userCanSignContract ? (
        <AppFormButton
          isSubmit
          className={styles['confirm-button']}
          isDisabled={
            processingSigningPage ||
            applicationCampaignUpdating ||
            isApplicationPayoutMethodUpdating ||
            (isBanklinkSigningAllowed && !isBanklinkOptionSelected)
          }
          label={t(LOCIZE_SIGNING_TRANSLATION_KEYS.signContract)}
        />
      ) : (
        <>
          <AppDisclaimer
            visible
            title={t(
              LOCIZE_SIGNING_TRANSLATION_KEYS.anotherLoginMethodDisclaimer,
            )}
            type={APP_DISCLAIMER_TYPES.error}
          />
          <AppLogOutButton
            type={LogoutButtonVariants.center}
            className={styles['logout-button']}
            onLogoutButtonClick={onLogOutClick}
          />
        </>
      )}
    </AppForm>
  );
};
