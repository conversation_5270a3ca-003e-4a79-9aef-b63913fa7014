import { IconNames } from 'app-constants';
import { AppCard, AppIcon, AppTooltip } from 'components';
import type { PayoutMethod, PayoutMethodConfig } from 'models';
import { useState } from 'react';

import styles from './Signing.module.scss';

type PayoutMethodCheckTabsProps = {
  payoutMethodConfig: PayoutMethodConfig[];
  defaultSelectedMethod: PayoutMethod;
  onPaymentMethodTabChecked: (method: PayoutMethod) => void;
};

export const PayoutMethodsCheckTabs = ({
  defaultSelectedMethod,
  payoutMethodConfig,
  onPaymentMethodTabChecked,
}: PayoutMethodCheckTabsProps) => {
  const [selectedMethod, setSelectedMethod] = useState<PayoutMethod>(
    defaultSelectedMethod,
  );
  return (
    <div className={styles['payment-methods-check-tabs']}>
      {payoutMethodConfig.map(({ method, title, description }) => (
        <AppCard
          containerClassName={
            styles['payment-methods-check-tabs__card-container']
          }
          cardClassName={styles['payment-methods-check-tabs__card']}
          key={method}
          active={selectedMethod === method}
          onClick={() => {
            if (selectedMethod === method) {
              return;
            }
            onPaymentMethodTabChecked(method);
            setSelectedMethod(method);
          }}
        >
          <div
            className={styles['payment-methods-check-tabs__card-top-content']}
          >
            <div className={styles['payment-methods-check-tabs__card-title']}>
              {title}
            </div>
            <AppTooltip
              tooltipLabel={description}
              className={
                styles['payment-methods-check-tabs__tooltip-container']
              }
            >
              <AppIcon name={IconNames.questionMark} />
            </AppTooltip>
          </div>
        </AppCard>
      ))}
    </div>
  );
};
