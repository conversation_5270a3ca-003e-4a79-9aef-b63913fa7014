import { AppPinTypes } from 'app-constants';
import { AppPinConfirmationComponent } from 'components';
import { useSigningPageContext } from 'context/small-loan';
import { useEffectOnce, useIsMobileDevice } from 'hooks';

let isSigning = false;
let isExecuted = false;
export const PinConfirmationView = () => {
  const {
    smartIdContractSignaturePollChallengeId,
    mobileIdContractSignaturePollChallengeId,
    onPinConfirmationCancel,
    signAppByMobileIdOrSmartId,
  } = useSigningPageContext();
  const isMobile = useIsMobileDevice();

  useEffectOnce(() => {
    const handleFocus = () => resignOnIosDeviceFocus();
    window.addEventListener('focus', handleFocus);

    const timeoutId = setTimeout(() => {
      signContract();
    }, 3000);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('focus', handleFocus);
    };
  });

  const signContract = () => {
    if (isExecuted || isSigning) {
      return;
    }

    isSigning = true;

    signAppByMobileIdOrSmartId()
      .then(() => {
        isExecuted = true;
      })
      .finally(() => {
        isSigning = false;
      });
  };

  const resignOnIosDeviceFocus = () => {
    if (isMobile) {
      signContract();
    }
  };

  return (
    <AppPinConfirmationComponent
      onCancel={onPinConfirmationCancel}
      pin={
        smartIdContractSignaturePollChallengeId ||
        mobileIdContractSignaturePollChallengeId
      }
      pinType={AppPinTypes.pin2}
      isCancelButtonVisible={!isExecuted && !isSigning}
    />
  );
};
