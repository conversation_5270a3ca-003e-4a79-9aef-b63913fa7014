import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  SigningPageViewTypes,
} from 'app-constants';
import { AppContactUs, AppLoader, AppLoaderContentWrapper } from 'components';
import { useSigningPageContext } from 'context/small-loan';
import { SigningPageLogicProvider } from 'context/small-loan/SigningPageLogicProvider';

import { PendingView } from './PendingView';
import { PinConfirmationView } from './PinConfirmationView';
import { SigningView } from './SigningView';

const Page = () => {
  const {
    processingSigningPage,
    signingPageLoaded,
    signingPageViewType,
    application,
  } = useSigningPageContext();

  if (!signingPageLoaded) {
    return <AppLoader isRelative />;
  }

  const renderSigningPageContent = () => {
    switch (signingPageViewType) {
      case SigningPageViewTypes.pinConfirmation:
        return <PinConfirmationView />;
      case SigningPageViewTypes.signing:
        return <SigningView />;
      case SigningPageViewTypes.pending:
        return <PendingView />;
      default:
        return <SigningView />;
    }
  };

  return (
    <AppLoaderContentWrapper shouldShowLoader={processingSigningPage}>
      {renderSigningPageContent()}
      <AppContactUs
        text={
          application?.merchant
            ? LOCIZE_COMMON_TRANSLATION_KEYS.contactUsAffiliateApplicationSigningDisclaimer
            : undefined
        }
      />
    </AppLoaderContentWrapper>
  );
};

const SigningPage = () => (
  <SigningPageLogicProvider>
    <Page />
  </SigningPageLogicProvider>
);

export default SigningPage;
