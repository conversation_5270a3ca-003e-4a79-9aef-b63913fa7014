@use 'styles/shared' as *;

.form {
  margin: to-rem(40px) 0;
}

.logout-button {
  padding: 8px 20px;
  border: to-rem(2px) solid $color-secondary-grey;
  border-radius: to-rem(18px);
}

.signing-disclaimer {
  @include paragraph-style(xs, $color-tertiary-dark);

  margin: to-rem(30px) 0;
  text-align: center;
}

.confirm-button {
  margin: to-rem(40px) auto;
  width: 100%;
}

.contract-button {
  margin: to-rem(40px) auto;
}

.small-loan-contract-button {
  background: $color-primary;

  &__icon {
    background: red;
  }
}

.heading {
  @include heading-style(md, $color-primary);

  margin-bottom: to-rem(30px);
  text-align: center;
}

.paragraph {
  @include paragraph-style(default, $color-primary);

  text-align: center;

  &:first-of-type {
    margin-top: to-rem(40px);
  }
}

.with-no-top-margin {
  margin-top: 0;
}

.disabled {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.5;
}

.payment-leave-toggler {
  margin-top: to-rem(40px);
}

.payment-methods-check-tabs {
  display: flex;
  gap: to-rem(8px);

  @include max-media(to-rem(450px)) {
    flex-wrap: wrap;
  }

  &__card-container {
    flex: 1;

    @include max-media(to-rem(450px)) {
      flex: unset;
    }
  }

  &__card {
    height: 100%;
    width: unset;
    padding: to-rem(16px);

    @include media(up-to-tablet) {
      width: 100%;
    }

    &-title {
      @include heading-style(sm);
    }

    &-description {
      @include paragraph-style(sm);

      margin-top: to-rem(8px);
    }
  }

  &__card-top-content {
    display: flex;
    justify-content: space-between;
  }

  &__tooltip-container {
    pointer-events: auto;
  }

  &__radio {
    margin-left: to-rem(10px);
    width: to-rem(24px);
    height: to-rem(24px);
    border-radius: to-rem(100px);
    background: $color-neutral-100;

    &--is-selected {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: to-rem(12px);
        height: to-rem(12px);
        border-radius: to-rem(100px);
        background: $color-primary;
      }
    }
  }
}

.iban-field {
  margin-top: to-rem(4px) !important;
}

.terms-label-link {
  color: $color-primary;
  text-decoration: underline;

  &:hover {
    opacity: 0.8;
  }
}
