import {
  AppLoaderSizes,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { AppLoader } from 'components';
import { useTranslation } from 'react-i18next';

import styles from './Signing.module.scss';

export const PendingView = () => {
  const { t } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  return (
    <div>
      <p className={styles.heading}>
        {t(LOCIZE_SIGNING_TRANSLATION_KEYS.signingContractLabel)}
      </p>
      <p className={styles.paragraph}>
        {tc(LOCIZE_COMMON_TRANSLATION_KEYS.dontClosePageLabel)}
      </p>

      <AppLoader
        className={styles['banklink-loader']}
        isRelative
        size={AppLoaderSizes.small}
      />
    </div>
  );
};
