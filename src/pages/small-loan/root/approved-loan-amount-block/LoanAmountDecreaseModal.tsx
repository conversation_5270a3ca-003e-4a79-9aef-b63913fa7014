import { zodResolver } from '@hookform/resolvers/zod';
import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Dialog } from 'components/dialog';
import { FormRangeInputField } from 'components/form/form-range-input-field/FormRangeInputField';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useRootContext } from 'context/root';
import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  LoanAmountDecreaseFormSchema,
  type LoanAmountFormType,
} from './schema';

const STEP = 10;

type LoanAmountDecreaseModalProps = {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (values: LoanAmountFormType) => void;
  currentAmount: number;
};

export const LoanAmountDecreaseModal = ({
  isOpen,
  onOpenChange,
  onConfirm,
  currentAmount,
}: LoanAmountDecreaseModalProps) => {
  const { applicationPrivateInfo } = useRootContext();
  const { t } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const { min_amount = 0, max_amount = 0 } = applicationPrivateInfo ?? {};

  const formSchema = useMemo(
    () =>
      LoanAmountDecreaseFormSchema({
        minLoanAmount: min_amount,
        maxLoanAmount: max_amount,
      }),
    [currentAmount],
  );

  const form = useForm<LoanAmountFormType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      [FormFieldNames.netTotal]: currentAmount,
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (isOpen) {
      form.reset({
        [FormFieldNames.netTotal]: currentAmount,
      });
    }
  }, [isOpen, currentAmount, form]);

  const handleConfirm = (values: LoanAmountFormType) => {
    return onConfirm(values);
  };

  return (
    <Dialog
      title={t(LOCIZE_SIGNING_TRANSLATION_KEYS.decreaseLoanAmountModalTitle)}
      open={isOpen}
      onOpenChange={onOpenChange}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleConfirm)}>
          <FormRangeInputField
            className="mt-4"
            control={form.control}
            name={FormFieldNames.netTotal}
            min={min_amount}
            max={currentAmount}
            step={STEP}
            disabled={form.formState.isSubmitting}
          />

          <Button
            fullWidth
            className="mt-12"
            variant="black"
            type="submit"
            loading={form.formState.isSubmitting}
            disabled={!form.formState.isDirty || !form.formState.isValid}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.confirm)}
          </Button>
        </form>
      </Form>
    </Dialog>
  );
};
