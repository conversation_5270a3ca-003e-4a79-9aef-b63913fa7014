import {
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import cn from 'clsx';
import { Typography } from 'components/typography';
import { useRootContext } from 'context/root';
import { useGetApplicationByReferenceSuspense } from 'hooks/use-get-application-by-reference-suspense';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { ConsumerLoanProduct } from 'models';
import { useTranslation } from 'react-i18next';
import { getConsumerLoanProductByScheduleType } from 'utils';

type SmallLoanTitleProps = {
  className?: string;
};

type SmallLoanHeaderConfigType = {
  title: string;
};

export const SmallLoanTitle = ({ className }: SmallLoanTitleProps) => {
  const { t } = useTranslation(LocizeNamespaces.checkout);

  const { user } = useRootContext();

  const { application } = (
    user
      ? useGetCurrentApplicationSuspense
      : useGetApplicationByReferenceSuspense
  )();

  const consumerLoanProduct = getConsumerLoanProductByScheduleType(
    application?.schedule_type,
  );

  const smallLoanHeaderConfig: SmallLoanHeaderConfigType = {
    [ConsumerLoanProduct.SMALL_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanMarketingTitle),
    },
    [ConsumerLoanProduct.FAST_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.fastLoanMarketingTitle),
    },
    [ConsumerLoanProduct.VEHICLE_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.vehicleLoanMarketingTitle),
    },
    [ConsumerLoanProduct.RENOVATION_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.renovationLoanMarketingTitle),
    },
    [ConsumerLoanProduct.TRAVEL_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.travelLoanMarketingTitle),
    },
    [ConsumerLoanProduct.HEALTH_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.healthLoanMarketingTitle),
    },
    [ConsumerLoanProduct.BEAUTY_LOAN]: {
      title: t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.beautyLoanMarketingTitle),
    },
  }[consumerLoanProduct];

  return (
    <Typography affects="bold" className={cn(className)}>
      {smallLoanHeaderConfig.title}
    </Typography>
  );
};
