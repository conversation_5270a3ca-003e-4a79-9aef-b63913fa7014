import {
  CreditLineRoutePaths,
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
} from 'app-constants';
import CircleArrowDownIcon from 'icons/circle-arrow-out-down-right.svg?react';
import CirclePercentIcon from 'icons/circle-percent.svg?react';
import FastForwardIcon from 'icons/fast-forward.svg?react';
import ShieldCheckIcon from 'icons/shield-check.svg?react';

export const SMALL_LOAN_INFO_PAGE_FEATURES = [
  {
    icon: FastForwardIcon,
    title: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureTitle1,
    description: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureDescription1,
  },
  {
    icon: CirclePercentIcon,
    title: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureTitle2,
    description: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureDescription2,
  },
  {
    icon: Shield<PERSON>heckIcon,
    title: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureTitle3,
    description: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureDescription3,
  },
  {
    icon: CircleArrowDownIcon,
    title: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureTitle4,
    description: LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanFeatureDescription4,
  },
];

import { SmallLoanRoutePaths } from 'app-constants';

export const SmallLoanWithMobileHeaderRoutePaths = [
  CreditLineRoutePaths.CONTACT,
  CreditLineRoutePaths.PURPOSE_OF_LOAN,
  CreditLineRoutePaths.CONTACT_EXTRA,
  CreditLineRoutePaths.SPOUSE,
  CreditLineRoutePaths.ACCOUNT_SCORING,
  CreditLineRoutePaths.EMTA_CONSENT,
  SmallLoanRoutePaths.INCOME_VERIFICATION,
];
