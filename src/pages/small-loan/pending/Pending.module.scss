@use 'styles/shared' as *;

.container {
  margin-top: to-rem(40px);

  .paragraph {
    @include paragraph-style;

    text-align: center;

    &:nth-child(2) {
      margin-top: to-rem(30px);
    }
  }

  .buttons {
    display: flex;
    flex-direction: column;
    gap: to-rem(20px);
    margin: to-rem(40px) 0 0;

    &.margin-top {
      margin-top: to-rem(16px);
    }

    .button {
      width: 100%;
    }
  }

  .heading {
    @include heading-style(md, $color-primary);

    margin-bottom: to-rem(30px);
    text-align: center;
  }

  .clock-icon {
    margin: to-rem(30px) 0;
  }
}
