@use 'styles/shared' as *;

.text {
  @include paragraph-style(default);

  &--bold {
    font-weight: $font-weight-medium;
  }

  &--mt-16 {
    margin-top: to-rem(16px);
  }

  &--mt-32 {
    margin-top: to-rem(32px);
  }

  &--center {
    text-align: center;
  }
}

.button {
  margin: to-rem(32px) auto;
}

.upload {
  margin: to-rem(32px) auto;
}

.divider {
  height: to-rem(1px);
  background: $color-tertiary;
  margin-top: to-rem(32px);
}

// AccountScoringStepper styles

.stepper-title {
  @include paragraph-style(sm, $color-primary);

  text-transform: uppercase;
}

.stepper {
  margin-top: to-rem(16px);
}

$step-state-size: 24px;

.step {
  display: flex;
  align-items: center;

  &-state {
    display: flex;
    justify-content: center;
    align-items: center;
    width: to-rem($step-state-size);
    height: to-rem($step-state-size);
    border-radius: 50%;
    border: to-rem(1px) solid $color-tertiary-dark;
    box-sizing: border-box;
    background-color: transparent;

    &--processing {
      background-color: $color-tertiary-dark;
      animation: change-color 1.87s ease infinite;
    }

    &--finished {
      background-color: $color-secondary-white;
      border: to-rem(1px) solid $color-secondary-white;
    }
  }

  &-label {
    color: $color-black;
    margin-left: to-rem(16px);
  }
}

.step-separator {
  height: to-rem(24px);
  width: to-rem(1px);
  background: $color-tertiary-dark;
  margin: to-rem(4px) to-rem(calc($step-state-size / 2));
}

@keyframes change-color {
  from {
    background: transparent;
    border-color: $color-tertiary-dark;
  }

  49% {
    background: $color-tertiary-dark;
    border-color: transparent;
  }

  51% {
    background: $color-tertiary-dark;
    border-color: transparent;
  }

  to {
    background: transparent;
    border-color: $color-tertiary-dark;
  }
}
