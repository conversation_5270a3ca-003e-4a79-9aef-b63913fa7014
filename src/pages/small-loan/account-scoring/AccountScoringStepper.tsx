import {
  ACCOUNT_SCORING_TRANSLATION_KEYS_BY_STEPS_MAP,
  AccountScoringSteps,
  AccountScoringStepState,
  IconNames,
  LocizeNamespaces,
} from 'app-constants';
import c from 'clsx';
import { AppIcon } from 'components';
import type { AccountScoringStepsType } from 'models';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import scssVars from 'styles/export.module.scss';
import { v4 as uuid } from 'uuid';

import styles from './AccountScoring.module.scss';

type AccountScoringStepperProps = {
  accountScoringSteps: AccountScoringStepsType;
};

export const AccountScoringStepper = memo(
  ({ accountScoringSteps }: AccountScoringStepperProps) => {
    const { t } = useTranslation(LocizeNamespaces.accountScoring);

    const renderAccountScoringSteps = () =>
      Object.keys(accountScoringSteps).map((stepCount) => {
        const step = Number(stepCount) as unknown as AccountScoringSteps;
        const stepState = accountScoringSteps[step];

        const shouldHaveSeparator = step !== AccountScoringSteps.finalizing;

        return (
          <div key={uuid()}>
            <div className={styles.step}>
              <div
                className={c(
                  styles['step-state'],
                  styles[`step-state--${stepState}`],
                )}
              >
                {stepState === AccountScoringStepState.finished ? (
                  <AppIcon
                    color={scssVars.colorSecondary}
                    name={IconNames.checkbox}
                  />
                ) : null}
              </div>
              <div className={styles['step-label']}>
                {t(ACCOUNT_SCORING_TRANSLATION_KEYS_BY_STEPS_MAP[step])}
              </div>
            </div>
            {shouldHaveSeparator ? (
              <div className={styles['step-separator']} />
            ) : null}
          </div>
        );
      });

    return <div className={styles.stepper}>{renderAccountScoringSteps()}</div>;
  },
);
