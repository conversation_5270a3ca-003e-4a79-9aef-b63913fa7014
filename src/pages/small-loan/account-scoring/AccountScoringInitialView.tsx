import {
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES,
  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import c from 'clsx';
import { AppButton, AppFileUpload } from 'components';
import { useTranslation } from 'react-i18next';

import styles from './AccountScoring.module.scss';

type AccountScoringInitialViewProps = {
  onAccountStatementUploaded: (statement: File) => void;
  onAccessBankButtonClick: () => void;
};

export const AccountScoringInitialView = ({
  onAccountStatementUploaded,
  onAccessBankButtonClick,
}: AccountScoringInitialViewProps) => {
  const { t } = useTranslation(LocizeNamespaces.accountScoring);
  const {
    accessBank,
    selectBankDisclaimer,
    incomeLiabilitiesDisclaimer,
    followInstructionsDisclaimer,
    uploadStatementDisclaimer1,
  } = LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS;
  return (
    <>
      <div className={styles.text}>{t(selectBankDisclaimer)}</div>
      <div className={c(styles.text, styles['text--mt-16'])}>
        {t(incomeLiabilitiesDisclaimer)}
      </div>
      <div
        className={c(styles.text, styles['text--bold'], styles['text--mt-16'])}
      >
        {t(followInstructionsDisclaimer)}
      </div>
      <AppButton
        className={styles.button}
        label={t(accessBank)}
        onClick={onAccessBankButtonClick}
      />
      <div
        className={c(
          styles.text,
          styles['text--mt-32'],
          styles['text--center'],
        )}
      >
        {t(uploadStatementDisclaimer1, {
          fileExtensions:
            ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES.join(', '),
        })}
      </div>
      <AppFileUpload
        acceptedFileTypes={ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES}
        className={styles.upload}
        onFileUploaded={onAccountStatementUploaded}
      />
    </>
  );
};
