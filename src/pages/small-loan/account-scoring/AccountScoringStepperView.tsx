import {
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES,
  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import c from 'clsx';
import { AppButton, AppFileUpload } from 'components';
import type { AccountScoringStepsType } from 'models';
import { useTranslation } from 'react-i18next';

import styles from './AccountScoring.module.scss';
import { AccountScoringStepper } from './AccountScoringStepper';

type AccountScoringStepperViewProps = {
  accountScoringSteps: AccountScoringStepsType;
  onAccountStatementUploaded: (statement: File) => void;
  onAccessBankButtonClick: () => void;
};

export const AccountScoringStepperView = ({
  accountScoringSteps,
  onAccountStatementUploaded,
  onAccessBankButtonClick,
}: AccountScoringStepperViewProps) => {
  const { t } = useTranslation(LocizeNamespaces.accountScoring);

  const { uploadStatementDisclaimer, verifyingFinancialInfo, accessBank } =
    LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS;

  return (
    <>
      <div className={styles['stepper-title']}>{t(verifyingFinancialInfo)}</div>
      <AccountScoringStepper {...{ accountScoringSteps }} />
      <div className={styles.divider} />
      <AppButton
        className={styles.button}
        label={t(accessBank)}
        onClick={onAccessBankButtonClick}
      />
      <div
        className={c(
          styles.text,
          styles['text--mt-32'],
          styles['text--center'],
        )}
      >
        {t(uploadStatementDisclaimer)}
      </div>
      <AppFileUpload
        acceptedFileTypes={ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES}
        className={styles.upload}
        onFileUploaded={onAccountStatementUploaded}
      />
    </>
  );
};
