import {
  APP_DISCLAIMER_TYPES,
  AppFormFieldTypes,
  Currencies,
  DEFAULT_NUMBER_OF_DEPENDENTS,
  FormFieldNames,
  FormTypes,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import c from 'clsx';
import {
  AppDisclaimer,
  AppForm,
  AppFormButton,
  AppFormField,
  AppFormNumberFieldWithCheck,
  AppLoader,
  AppLoaderContentWrapper,
} from 'components';
import { isLtRegion } from 'environment';
import { useContactExtraPageLogic } from 'hooks/page-logic/small-loan';
import { useTranslation } from 'react-i18next';

import { AddCompanyDetailsField } from './AddCompanyDetailsField';
import styles from './ContactExtra.module.scss';
import { SpouseSendInstructions } from './SpouseSendInstructions';

const ContactExtraPage = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const {
    onAddLegalPersonToInvoiceChange,
    onContactExtraFormSubmit,
    contactExtraPageFormConfig,
    contactExtraPageLoaded,
    visiblePageAttributes,
    onChangeTotalExpenses,
    processingContactExtraPage,
    userInfoExtraValidationErrors,
    sendingConsentLinkValidationErrors,
    setFormType,
    formType,
    instructionsSent,
    setInstructionsSent,
    sendingConsentLink,
    occupationCategoryOptions,
    legalPeopleOptions,
    employmentDateOptions,
    addLegalPersonToInvoiceDisabled,
    legalPeopleLoading,
  } = useContactExtraPageLogic();

  if (!contactExtraPageLoaded) {
    return <AppLoader isRelative />;
  }

  return (
    <AppLoaderContentWrapper shouldShowLoader={processingContactExtraPage}>
      <AppDisclaimer
        title={tc(LOCIZE_COMMON_TRANSLATION_KEYS.checkIncomeDisclaimer)}
        type={APP_DISCLAIMER_TYPES.warning}
        visible={
          visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer]
        }
        withNoTopMargin
      />

      {isLtRegion ||
        (visiblePageAttributes[
          PageAttributeNames.spouseInstructionsSection
        ] && (
          <div className={c(styles.section__title, styles.margin__top_40)}>
            {t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.personalSectionTitle)}
          </div>
        ))}

      <AppForm
        className={c(styles.form, {
          [styles['form--margin-top']]:
            !visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer],
        })}
        formConfig={contactExtraPageFormConfig}
        onSubmit={onContactExtraFormSubmit}
      >
        <AppFormField
          currency={Currencies.euro}
          invalid={
            userInfoExtraValidationErrors[FormFieldNames.netIncomeMonthly]
          }
          label={t(
            isLtRegion
              ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabelLt
              : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabel,
          )}
          name={FormFieldNames.netIncomeMonthly}
          placeholder={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.perMonthPlaceholder,
          )}
          tooltipLabel={t(
            isLtRegion
              ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabelLt
              : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabel,
          )}
          type={AppFormFieldTypes.number}
          visible={visiblePageAttributes[PageAttributeNames.netIncomeMonthly]}
          withTooltip
        />

        <div className={styles['sum-of-expenses']}>
          <AppFormField
            allowLeadingZero
            className={styles['sum-of-expenses__field']}
            currency={Currencies.euro}
            invalid={
              userInfoExtraValidationErrors[FormFieldNames.expenditureMonthly]
            }
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureLabel)}
            name={FormFieldNames.expenditureMonthly}
            onChange={onChangeTotalExpenses}
            placeholder={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.perMonthPlaceholder,
            )}
            tooltipLabel={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureTooltipLabel,
            )}
            type={AppFormFieldTypes.number}
            visible={
              visiblePageAttributes[PageAttributeNames.expenditureMonthly]
            }
            withTooltip
          />
          <AppFormField
            allowLeadingZero
            className={styles['sum-of-expenses__field']}
            currency={Currencies.euro}
            invalid={
              userInfoExtraValidationErrors[
                FormFieldNames.monthlyLivingExpenses
              ]
            }
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthLabel,
            )}
            name={FormFieldNames.monthlyLivingExpenses}
            onChange={onChangeTotalExpenses}
            placeholder={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.perMonthPlaceholder,
            )}
            tooltipLabel={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthTooltipLabel,
            )}
            type={AppFormFieldTypes.number}
            visible={
              visiblePageAttributes[PageAttributeNames.monthlyLivingExpenses]
            }
            withTooltip
          />
        </div>

        <AppFormField
          isDisabled
          currency={Currencies.euro}
          invalid={userInfoExtraValidationErrors[FormFieldNames.totalExpenses]}
          label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.totalExpensesLabel)}
          name={FormFieldNames.totalExpenses}
          placeholder={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.perMonthPlaceholder,
          )}
          type={AppFormFieldTypes.number}
          visible={
            visiblePageAttributes[PageAttributeNames.expenditureMonthly] &&
            visiblePageAttributes[PageAttributeNames.monthlyLivingExpenses]
          }
        />

        <AppFormNumberFieldWithCheck
          withTooltip
          tooltipLabel={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtTooltipLabel,
          )}
          allowLeadingZero
          checkLabel={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtCheckboxLabel,
          )}
          fieldLabel={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtLabel)}
          fieldName={FormFieldNames.overdueDebt}
          visible={visiblePageAttributes[PageAttributeNames.overdueDebt]}
        />

        <AppFormField
          className={styles['field-select']}
          invalid={
            userInfoExtraValidationErrors[FormFieldNames.occupationCategory]
          }
          label={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryFieldLabel,
          )}
          name={FormFieldNames.occupationCategory}
          options={occupationCategoryOptions}
          placeholder={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryFieldPlaceholder,
          )}
          type={AppFormFieldTypes.select}
          visible={
            visiblePageAttributes[PageAttributeNames.occupationCategoryDropdown]
          }
        />

        <AppFormField
          allowLeadingZero
          digitsAfterDecimalPoint={0}
          invalid={
            userInfoExtraValidationErrors[FormFieldNames.numberOfDependents]
          }
          label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.dependentsLabel)}
          name={FormFieldNames.numberOfDependents}
          placeholder={DEFAULT_NUMBER_OF_DEPENDENTS}
          type={AppFormFieldTypes.number}
          visible={visiblePageAttributes[PageAttributeNames.numberOfDependents]}
        />

        <AppFormField
          invalid={userInfoExtraValidationErrors[FormFieldNames.employmentDate]}
          label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.employmentDateLabel)}
          name={FormFieldNames.employmentDate}
          options={employmentDateOptions}
          placeholder={tc(LOCIZE_COMMON_TRANSLATION_KEYS.chooseOptionLabel)}
          type={AppFormFieldTypes.select}
          visible={visiblePageAttributes[PageAttributeNames.employmentDate]}
        />

        <AppFormNumberFieldWithCheck
          allowLeadingZero
          checkLabel={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsCheckboxLabel,
          )}
          fieldLabel={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsLabel,
          )}
          fieldName={FormFieldNames.planningNewDebts}
          visible={visiblePageAttributes[PageAttributeNames.planningNewDebts]}
        />

        <AppFormNumberFieldWithCheck
          checkLabel={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsCheckboxLabel,
          )}
          fieldLabel={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsLabel,
          )}
          fieldName={FormFieldNames.futureReducedEarnings}
          visible={
            visiblePageAttributes[PageAttributeNames.futureReducedEarnings]
          }
        />

        <AppFormField
          label={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerLabel,
          )}
          name={FormFieldNames.ultimateBeneficialOwner}
          tooltipLabel={t(
            LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerTooltip,
          )}
          type={AppFormFieldTypes.checkbox}
          visible={
            visiblePageAttributes[PageAttributeNames.ultimateBeneficialOwner]
          }
          withTooltip
        />

        <AddCompanyDetailsField
          isDisabled={addLegalPersonToInvoiceDisabled}
          legalPeopleLoading={legalPeopleLoading}
          onChange={onAddLegalPersonToInvoiceChange}
          selectOptions={legalPeopleOptions}
          visible={
            visiblePageAttributes[PageAttributeNames.addLegalPersonToInvoice]
          }
        />

        <SpouseSendInstructions
          formType={formType}
          instructionsSent={instructionsSent}
          sendingConsentLink={sendingConsentLink}
          sendingConsentLinkValidationErrors={
            sendingConsentLinkValidationErrors
          }
          setFormType={setFormType}
          setInstructionsSent={setInstructionsSent}
          visible={
            visiblePageAttributes[PageAttributeNames.spouseInstructionsSection]
          }
        />

        <AppFormButton
          className={styles['submit-button']}
          isDisabled={
            (visiblePageAttributes[
              PageAttributeNames.spouseInstructionsSection
            ] &&
              !instructionsSent) ||
            processingContactExtraPage
          }
          isSubmit
          label={t(LOCIZE_COMMON_TRANSLATION_KEYS.continue, {
            ns: LocizeNamespaces.common,
          })}
          onClick={() => setFormType(FormTypes.primary)}
        />
      </AppForm>
    </AppLoaderContentWrapper>
  );
};

export default ContactExtraPage;
