import { PoliticalExposure } from 'api/core/generated';
import {
  APP_DISCLAIMER_TYPES,
  AppFormFieldTypes,
  FormFieldNames,
  LOCIZE_CONTACT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { AppDisclaimer, AppFormField } from 'components';
import type { Option } from 'models';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

type PoliticallyExposedPersonFieldProps = {
  visible: boolean;
  options: Array<Option>;
};

export const PoliticallyExposedPersonField = ({
  visible,
  options,
}: PoliticallyExposedPersonFieldProps) => {
  const { t } = useTranslation(LocizeNamespaces.contact);

  const formMethods = useFormContext();
  const { watch } = formMethods;

  const politicalExposure = watch(FormFieldNames.politicalExposure);
  const noPoliticallyExposedOption = options.find(
    ({ value }) => value === PoliticalExposure.NONE,
  );

  const shouldShowPoliticalExposureDisclaimer = !(
    noPoliticallyExposedOption?.value === politicalExposure ||
    noPoliticallyExposedOption?.label === politicalExposure
  );

  if (!visible) {
    return null;
  }

  return (
    <>
      <AppFormField
        label={t(
          LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonFieldLabel,
        )}
        name={FormFieldNames.politicalExposure}
        options={options}
        placeholder={t(
          LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonPlaceholderLabel,
        )}
        tooltipLabel={t(
          LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonTooltipLabel,
        )}
        type={AppFormFieldTypes.select}
        visible
        withTooltip
      />
      <AppDisclaimer
        title={t(
          LOCIZE_CONTACT_TRANSLATION_KEYS.politicallyExposedPersonDisclaimerLabel,
        )}
        type={APP_DISCLAIMER_TYPES.error}
        visible={shouldShowPoliticalExposureDisclaimer}
        withSmallTopMargin
      />
    </>
  );
};
