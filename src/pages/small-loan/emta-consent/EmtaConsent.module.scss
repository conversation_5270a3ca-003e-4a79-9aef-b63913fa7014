@use 'styles/shared' as *;

.container {
  margin-top: to-rem(40px);
}

.heading {
  @include heading-style(md, $color-primary);

  text-align: center;
}

.description {
  @include paragraph-style(default);

  margin-top: to-rem(20px);
  text-align: center;
}

.income-source-options-buttons {
  margin-top: to-rem(40px);
}

.income-source-option-button {
  text-align: center;
  box-sizing: border-box;
  padding: to-rem(20px);
  border: to-rem(2px) solid $color-tertiary-10;
  border-radius: to-rem(10px);
  cursor: pointer;

  &:not(:first-child) {
    margin-top: to-rem(20px);
  }

  &:hover:not(.income-source-option-button--active) {
    border-color: $color-tertiary-dark;
  }

  &--active {
    border-color: $color-primary;
  }

  &__title {
    @include heading-style(sm, $color-primary);
  }

  &__description {
    @include paragraph-style(default);

    margin-top: to-rem(15px);
  }
}

.continue-button {
  margin-top: to-rem(40px);
  width: 100%;
}

.retry-buttons-container {
  background: $color-secondary-white;
  border-radius: to-rem(16px);
  margin-top: to-rem(120px);
  display: flex;
  flex-direction: column;
  padding: to-rem(24px);
  gap: to-rem(16px);
  width: 100%;
}

.retry-buttons-disclaimer {
  @include heading-style(sm);

  text-align: center;
  margin-bottom: to-rem(8px);
}
