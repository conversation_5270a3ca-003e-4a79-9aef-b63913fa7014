import {
  ButtonSizes,
  ButtonVariants,
  EMTA_DELAYED_RESPONSE_TIMEOUT,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import c from 'clsx';
import { AppButton, AppLoader } from 'components';
import { useEmtaConsentPageLogic } from 'hooks/page-logic/small-loan';
import { useTranslation } from 'react-i18next';

import styles from './EmtaConsent.module.scss';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.emtaConsent);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    incomeSourcesCheckServicesOptions,
    emtaConsentPageProcessing,
    onContinueButtonClick,
    returnedFromEmtaService,
    onEmtaRedirectButtonClick,
    onAccountScoringRedirectButtonClick,
  } = useEmtaConsentPageLogic();

  const renderIncomeSourcesCheckServicesOptions = () => (
    <div className={styles['income-source-options-buttons']}>
      {incomeSourcesCheckServicesOptions.map(
        ({ id, onClick, isActive, title, description }) => (
          <div
            key={id}
            className={c(styles['income-source-option-button'], {
              [styles['income-source-option-button--active']]: isActive,
            })}
            onClick={onClick}
          >
            <div className={styles['income-source-option-button__title']}>
              {title}
            </div>
            <div className={styles['income-source-option-button__description']}>
              {description}
            </div>
          </div>
        ),
      )}
    </div>
  );

  if (emtaConsentPageProcessing) {
    return (
      <AppLoader
        isRelative
        withDelayedAfter={returnedFromEmtaService}
        delayedAfterTimeout={EMTA_DELAYED_RESPONSE_TIMEOUT}
        after={
          <div className={styles['retry-buttons-container']}>
            <span className={styles['retry-buttons-disclaimer']}>
              {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.retryEmtaDisclaimer)}
            </span>
            <AppButton
              size={ButtonSizes.medium}
              label={t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.retryEmtaButton)}
              onClick={onEmtaRedirectButtonClick}
            />
            <AppButton
              size={ButtonSizes.medium}
              variant={ButtonVariants.secondary}
              label={t(
                LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.retryAccountScoringButton,
              )}
              onClick={onAccountScoringRedirectButtonClick}
            />
          </div>
        }
      />
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.heading}>
        {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaConsentTitle)}
      </div>
      <div className={styles.description}>
        {t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaConsentDescription)}
      </div>
      {renderIncomeSourcesCheckServicesOptions()}
      <AppButton
        className={styles['continue-button']}
        label={tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        onClick={onContinueButtonClick}
      />
    </div>
  );
};

export default Page;
