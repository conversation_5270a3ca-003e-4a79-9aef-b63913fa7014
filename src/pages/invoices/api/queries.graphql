query Invoices($page: Int, $userId: Int!) {
  invoices(limit: 8, page: $page, direction: desc, user_id: $userId) {
    data {
      id
      due_at
      url
      total_amount
      from_date
      type
      merchant_invoice {
        paid
      }
    }
    last_page
    total
  }
}

query GracePeriodAccessability {
  me(is_me: true) {
    active_payment_leave {
      id
      end_date
    }
    active_applications {
      id
    }
    credit_accounts {
      id
      status
    }
  }
}
