import { Badge } from '@components/ui/badge';
import { Button } from '@components/ui/button';
import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useTranslation } from 'react-i18next';

import { cn } from '@/shared/utils/tailwind';

type Props = {
  invoiceId: number;
  type: string;
  totalAmount: number;
  createdAt: string;
  isPaid?: boolean;
};

// Constants for status indicators
const OVERDUE_COLORS = {
  RED_PRIMARY: '#DC2626',
  RED_HOVER: '#B91C1C',
  RED_ACTIVE: '#991B1B',
  GREEN_PRIMARY: '#10B981',
  BLUE_PRIMARY: '#3B82F6',
} as const;

const OVERDUE_THRESHOLDS = {
  NEW_INVOICE_DAYS: 7,
} as const;

// Utility function to check if invoice is new
const isInvoiceNew = (createdAt: string): boolean => {
  if (!createdAt) return false;
  const createdDate = new Date(createdAt);
  const thresholdDate = new Date(
    Date.now() - OVERDUE_THRESHOLDS.NEW_INVOICE_DAYS * 24 * 60 * 60 * 1000,
  );
  return createdDate > thresholdDate;
};

export const InvoiceStatusIndicators = ({
  totalAmount,
  createdAt,
  isPaid = false,
}: Props) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.common);

  const isNew = isInvoiceNew(createdAt);
  const isUnsettled = totalAmount > 0 && !isPaid;

  const badgeColors = {
    green: OVERDUE_COLORS.GREEN_PRIMARY,
    blue: OVERDUE_COLORS.BLUE_PRIMARY,
  };

  // Randomly choose between green and blue for new badge
  const newBadgeColor = Math.random() > 0.5 ? 'green' : 'blue';

  return (
    <div className="flex items-center gap-2">
      {isNew && (
        <Badge
          className={cn(
            'text-white text-xs px-2 py-1 rounded-md font-semibold',
          )}
          style={{
            backgroundColor: badgeColors[newBadgeColor],
          }}
        >
          {t(LOCIZE_COMMON_KEYS.new)}
        </Badge>
      )}

      {isUnsettled && (
        <Button
          size="small"
          className={cn(
            'w-4 h-4 min-w-4 min-h-4 rounded-full p-0 cursor-default',
            'focus:ring-0 focus:ring-offset-0',
          )}
          style={{
            backgroundColor: OVERDUE_COLORS.RED_PRIMARY,
            zIndex: -1,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = OVERDUE_COLORS.RED_HOVER;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = OVERDUE_COLORS.RED_PRIMARY;
          }}
          onMouseDown={(e) => {
            e.currentTarget.style.backgroundColor = OVERDUE_COLORS.RED_ACTIVE;
          }}
          onMouseUp={(e) => {
            e.currentTarget.style.backgroundColor = OVERDUE_COLORS.RED_HOVER;
          }}
        />
      )}
    </div>
  );
};
