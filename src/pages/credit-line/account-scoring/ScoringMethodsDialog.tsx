import {
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES,
  AppRegions,
  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Dialog } from 'components/dialog';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Input } from 'components/ui/input';
import { Separator } from 'components/ui/separator';
import { region } from 'environment';
import PaperClipIcon from 'icons/paper-clip.svg?react';
import TrashIcon from 'icons/trash.svg?react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

const ACCEPTED_FILE_TYPES =
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES.join(', ');

type ScoringMethodsDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAccountStatementUploaded: (statement: File, callback?: () => void) => void;
  uploadAccountStatementProcessing: boolean;
  isEmtaButtonDisabled: boolean;
  isRedirectingToEmta: boolean;
  onEmtaRedirectButtonClick: () => void;
};

const isEmtaSectionVisible = region === AppRegions.et;

export const ScoringMethodsDialog = ({
  open,
  onOpenChange,
  isRedirectingToEmta,
  uploadAccountStatementProcessing,
  onAccountStatementUploaded,
  onEmtaRedirectButtonClick,
  isEmtaButtonDisabled,
}: ScoringMethodsDialogProps) => {
  const { t } = useTranslation(LocizeNamespaces.accountScoringV2);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const [uploadedStatement, setUploadedStatement] = useState<File | null>(null);

  return (
    <Dialog
      title={t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsTitle)}
      open={open}
      onOpenChange={onOpenChange}
    >
      <Typography>
        {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsDescription)}
      </Typography>

      <Separator className="my-6" />

      {isEmtaSectionVisible && (
        <>
          <section className="space-y-4">
            <Typography variant="xxs">
              {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.emtaTitle)}
            </Typography>
            <Typography variant="text-s">
              {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.emtaDescription)}
            </Typography>
            <Button
              size="small"
              className="mt-6"
              loading={isRedirectingToEmta}
              disabled={isEmtaButtonDisabled}
              onClick={() => {
                onEmtaRedirectButtonClick();
                onOpenChange(false);
              }}
            >
              {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.startButton)}
            </Button>
          </section>
          <Separator className="my-6" />
        </>
      )}

      <section className="space-y-4">
        <Typography variant="xxs">
          {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadBankStatementTitle)}
        </Typography>
        <Typography variant="text-s">
          {t(
            LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadBankStatementDescription,
          )}
        </Typography>
        <Typography variant="text-s" className="text-neutral-500 mt-4">
          {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.acceptedFileTypesLabel, {
            acceptedFileTypes: ACCEPTED_FILE_TYPES,
          })}
        </Typography>

        <div
          className={cn(
            'transition-all duration-500 ease-in-out max-h-[500px] opacity-100 overflow-hidden p-2 rounded-lg bg-neutral-50 border border-solid border-neutral-200 flex items-center gap-2 !mt-6',
            !uploadedStatement && 'max-h-0 opacity-0 py-0 !mt-0',
          )}
        >
          <PaperClipIcon />
          <Typography className="w-full" variant="text-s" affects="bold">
            {uploadedStatement?.name}
          </Typography>
          <Button
            size="small"
            className="px-2 ml-auto"
            variant="transparent"
            onClick={() => {
              setUploadedStatement(null);
              const input = document.getElementById('file-upload');
              if (input && input instanceof HTMLInputElement) {
                input.value = '';
              }
            }}
          >
            <TrashIcon />
          </Button>
        </div>

        <Input
          className="hidden"
          id="file-upload"
          type="file"
          accept={ACCEPTED_FILE_TYPES}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              setUploadedStatement(file);
            }
          }}
        />
        {!uploadedStatement && (
          <Button
            size="small"
            className="!mt-6"
            onClick={() => {
              document.getElementById('file-upload')?.click();
            }}
          >
            {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadButton)}
          </Button>
        )}
        {uploadedStatement && (
          <Button
            size="small"
            className="!mt-6"
            loading={uploadAccountStatementProcessing}
            onClick={() => {
              onAccountStatementUploaded(uploadedStatement, () => {
                onOpenChange(false);
              });
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
          </Button>
        )}
      </section>
    </Dialog>
  );
};
