import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import { FormSelectField } from 'components/form/form-select-field';
import { WarningNotification } from 'components/notification';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useRootContext } from 'context/root';
import { isLtRegion } from 'environment';
import {
  type ContactExtraFormType,
  useContactExtraPageLogic,
} from 'hooks/page-logic/credit-line';
import { useTranslation } from 'react-i18next';

const ContactExtraPage = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();
  const {
    onContactExtraFormSubmit,
    visiblePageAttributes,
    userInfoExtraValidationErrors,
    occupationCategoryOptions,
    employmentDateOptions,
    form,
  } = useContactExtraPageLogic();

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onContactExtraFormSubmit)}
        className="grid w-full gap-2"
      >
        {visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer] ? (
          <WarningNotification className="mb-10">
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.checkIncomeDisclaimer2)}
          </WarningNotification>
        ) : null}

        {visiblePageAttributes[
          PageAttributeNames.occupationCategoryDropdown
        ] ? (
          <FormSelectField<ContactExtraFormType>
            control={form.control}
            name={FormFieldNames.occupationCategory}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryFieldLabel,
            )}
            options={occupationCategoryOptions}
            invalid={
              userInfoExtraValidationErrors[FormFieldNames.occupationCategory]
            }
            disabled={
              form.formState.isSubmitting || !occupationCategoryOptions.length
            }
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.netIncomeMonthly] ? (
          <FormNumberInputField<ContactExtraFormType>
            control={form.control}
            disabled={form.formState.isSubmitting}
            name={FormFieldNames.netIncomeMonthly}
            label={t(
              isLtRegion
                ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabelLt
                : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabel,
            )}
            info={t(
              isLtRegion
                ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabelLt
                : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabel,
            )}
            invalid={
              userInfoExtraValidationErrors[FormFieldNames.netIncomeMonthly]
            }
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.expenditureMonthly] ? (
          <FormNumberInputField<ContactExtraFormType>
            control={form.control}
            disabled={form.formState.isSubmitting}
            name={FormFieldNames.expenditureMonthly}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureLabel)}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureTooltipLabel,
            )}
            invalid={
              userInfoExtraValidationErrors[FormFieldNames.expenditureMonthly]
            }
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.monthlyLivingExpenses] ? (
          <FormNumberInputField<ContactExtraFormType>
            control={form.control}
            disabled={form.formState.isSubmitting}
            name={FormFieldNames.monthlyLivingExpenses}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthLabel,
            )}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthTooltipLabel,
            )}
            invalid={
              userInfoExtraValidationErrors[
                FormFieldNames.monthlyLivingExpenses
              ]
            }
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.numberOfDependents] ? (
          <FormNumberInputField<ContactExtraFormType>
            control={form.control}
            name={FormFieldNames.numberOfDependents}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.dependentsLabel)}
            allowLeadingZeros
            fixedDecimalScale={true}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoExtraValidationErrors[FormFieldNames.numberOfDependents]
            }
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.employmentDate] ? (
          <FormSelectField<ContactExtraFormType>
            control={form.control}
            name={FormFieldNames.employmentDate}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.employmentDateLabel)}
            options={employmentDateOptions}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoExtraValidationErrors[FormFieldNames.employmentDate]
            }
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.overdueDebt] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(FormFieldNames.overdueDebt) ||
              userInfoExtraValidationErrors[FormFieldNames.overdueDebt]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(FormFieldNames.overdueDebt, null);
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtCheckboxLabel,
            )}
            disabled={form.formState.isSubmitting}
            invalid={userInfoExtraValidationErrors[FormFieldNames.overdueDebt]}
          >
            <FormNumberInputField<ContactExtraFormType>
              control={form.control}
              name={FormFieldNames.overdueDebt}
              label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtLabel)}
              info={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtTooltipLabel,
              )}
              disabled={form.formState.isSubmitting}
              invalid={
                userInfoExtraValidationErrors[FormFieldNames.overdueDebt]
              }
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.planningNewDebts] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(FormFieldNames.planningNewDebts) ||
              userInfoExtraValidationErrors[FormFieldNames.planningNewDebts]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(FormFieldNames.planningNewDebts, null);
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsCheckboxLabel,
            )}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoExtraValidationErrors[FormFieldNames.planningNewDebts]
            }
          >
            <FormNumberInputField<ContactExtraFormType>
              control={form.control}
              name={FormFieldNames.planningNewDebts}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsLabel,
              )}
              disabled={form.formState.isSubmitting}
              invalid={
                userInfoExtraValidationErrors[FormFieldNames.planningNewDebts]
              }
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.futureReducedEarnings] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(FormFieldNames.futureReducedEarnings) ||
              userInfoExtraValidationErrors[
                FormFieldNames.futureReducedEarnings
              ]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(FormFieldNames.futureReducedEarnings, null);
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsCheckboxLabel,
            )}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoExtraValidationErrors[
                FormFieldNames.futureReducedEarnings
              ]
            }
          >
            <FormNumberInputField<ContactExtraFormType>
              control={form.control}
              disabled={form.formState.isSubmitting}
              name={FormFieldNames.futureReducedEarnings}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsLabel,
              )}
              invalid={
                userInfoExtraValidationErrors[
                  FormFieldNames.futureReducedEarnings
                ]
              }
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.ultimateBeneficialOwner] ? (
          <FormCheckboxField<ContactExtraFormType>
            containerClassName="mt-2 px-2.5"
            control={form.control}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerLabel,
            )}
            name={FormFieldNames.ultimateBeneficialOwner}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerTooltip,
            )}
            disabled={form.formState.isSubmitting}
            invalid={
              userInfoExtraValidationErrors[
                FormFieldNames.ultimateBeneficialOwner
              ]
            }
          />
        ) : null}

        <Button
          className="mt-12"
          disabled={
            !form.formState.isSubmitting && pageUrlAndNavigationProcessing
          }
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>
        <Button
          fullWidth
          className="mt-2"
          variant="white"
          loading={
            !form.formState.isSubmitting && pageUrlAndNavigationProcessing
          }
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(false);
          }}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
      </form>
    </Form>
  );
};

export default ContactExtraPage;
