import {
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_SUCCESS_TRANSLATION_KEYS,
  LocizeNamespaces,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { Typography } from 'components/typography/Typography';
import { Card, CardContent } from 'components/ui/card';
import { Checkbox } from 'components/ui/checkbox';
import { useRootContext } from 'context/root';
import { useRudderStack, useUpdateUserTerms } from 'hooks';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { cn } from 'utils/tailwind';

export const NewsLetterAgreementSection = ({
  className,
}: {
  className?: string;
}) => {
  const { ruderStackEvents } = useRudderStack();
  const { t } = useTranslation(LocizeNamespaces.success);
  const { t: te } = useTranslation(LocizeNamespaces.errors);
  const { user } = useRootContext();
  const { updateUserTerms, userTermsUpdating, userTermsUpdated } =
    useUpdateUserTerms();

  const [isHidden, setIsHidden] = useState(false);
  const [checked, setChecked] = useState(false);

  const onSubscriptionCardClick = () => {
    if (userTermsUpdating || userTermsUpdated) return;
    setChecked(true);
    confirmSubscription();
  };

  const confirmSubscription = async () => {
    if (!user) {
      throw new Error('User is not defined');
    }

    try {
      const result = await updateUserTerms({
        user_id: user.id,
        newsletter_agreement: true,
        conditions_agreement: true,
        allow_pension_query: user.allow_pension_query,
      });

      if (!result.data?.update_user_terms) {
        setChecked(false);
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        return;
      }

      ruderStackEvents.marketingConsentOnSuccess({ userId: user.id });
    } catch (error) {
      setChecked(false);
      console.warn('Error during updating user terms', error);
    }
  };

  useEffect(() => {
    if (userTermsUpdated) {
      setTimeout(() => {
        setIsHidden(true);
      }, 2000);
    }
  }, [userTermsUpdated]);

  return (
    <div
      className={cn(
        'transition-all duration-500 ease-in-out max-h-[500px] opacity-100',
        isHidden && 'max-h-0 opacity-0 py-0',
        !isHidden && className,
      )}
    >
      <Card
        onClick={onSubscriptionCardClick}
        className="max-h-[500px] cursor-pointer"
      >
        <CardContent className="p-4 border-b">
          <div className="flex flex-row gap-4 items-center">
            <Checkbox
              //onClick={onConfirmSubscriptionButtonClick}
              disabled={userTermsUpdating || userTermsUpdated}
              checked={checked}
            />

            <Typography
              variant="text-s"
              className={cn(
                'transition-all duration-500 ease-in-out',
                userTermsUpdated && 'hidden',
              )}
            >
              {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.newsletterAgreementTitle2)}
            </Typography>
            <Typography
              variant="text-s"
              className={cn(
                'transition-all duration-500 ease-in-out',
                !userTermsUpdated && 'hidden',
              )}
            >
              {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.successLabel)}
            </Typography>
          </div>
        </CardContent>
      </Card>

      <div
        className={cn(
          'p-4 text-center',
          'transition-all duration-[850ms] ease-in-out max-h-[500px] opacity-100 overflow-hidden',
          userTermsUpdated && 'max-h-0 opacity-0 py-0',
        )}
      >
        <Typography variant="text-s" tag="div" className="text-neutral-500">
          <AppLocalizationComponent
            locizeKey={
              LOCIZE_SUCCESS_TRANSLATION_KEYS.newsletterAgreementDisclaimer
            }
            components={{
              privacyLink: <AppExternalLink to={REDIRECT_URLS.privacyPolicy} />,
            }}
            t={t}
          />
        </Typography>
      </div>
    </div>
  );
};
