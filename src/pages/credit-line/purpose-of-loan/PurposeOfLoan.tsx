import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { RadioGroup } from '@radix-ui/react-radio-group';
import { PurposeOfLoanType } from 'api/core/generated';
import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_LOAN_TYPE_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { FormInputField } from 'components/form/form-input-field';
import { FormPhoneField } from 'components/form/form-phone-field';
import { FormSelectField } from 'components/form/form-select-field';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from 'components/ui/form';
import { RadioGroupItem } from 'components/ui/radio-group';
import { useRootContext } from 'context/root';
import { useSendConsentLinkToSpouse, useUpdateUserInfo } from 'hooks';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { formatUserToPurposeOfLoanPageDataFormat } from 'services';
import { processGqlFormValidationErrors } from 'utils/parseGraphQLError';
import { cn } from 'utils/tailwind';
import * as z from 'zod';

const purposeOfLoanFormSchema = z
  .object({
    [FormFieldNames.purposeOfLoan]: z.nativeEnum(PurposeOfLoanType),
    [FormFieldNames.spouseApplicationMethod]: z.string(),
    [FormFieldNames.spouseEmail]: z.string(),
    [FormFieldNames.spousePhone]: z.string(),
  })
  .refine(
    ({
      spouse_application_method,
      spouse_email,
      spouse_phone,
      purpose_of_loan,
    }) => {
      if (purpose_of_loan === PurposeOfLoanType.MARRIED_HOUSEHOLD) {
        // Check if application method is selected
        if (!spouse_application_method) {
          return false;
        }

        // Check email format if email method selected
        if (spouse_application_method === FormFieldNames.spouseEmail) {
          return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(spouse_email);
        }

        // Check phone if phone method selected
        if (spouse_application_method === FormFieldNames.spousePhone) {
          return !!spouse_phone;
        }
      }

      return true;
    },
    (data) => {
      if (data.purpose_of_loan === PurposeOfLoanType.MARRIED_HOUSEHOLD) {
        if (!data.spouse_application_method) {
          return {
            message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
            path: [FormFieldNames.spouseApplicationMethod],
          };
        }

        if (
          data.spouse_application_method === FormFieldNames.spouseEmail &&
          !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(data.spouse_email)
        ) {
          return {
            message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
            path: [FormFieldNames.spouseEmail],
          };
        }

        if (
          data.spouse_application_method === FormFieldNames.spousePhone &&
          !data.spouse_phone
        ) {
          return {
            message: LOCIZE_ERRORS_TRANSLATION_KEYS.validationRequired,
            path: [FormFieldNames.spousePhone],
          };
        }
      }

      return { message: '', path: [] };
    },
  );

type PurposeOfLoanFormSchema = z.infer<typeof purposeOfLoanFormSchema>;

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.common);
  const { t: tp } = useTranslation(LocizeNamespaces.purposeOfLoan);

  const spouseApplicationMethodOptions = useMemo(
    () => [
      {
        value: FormFieldNames.spouseEmail,
        label: tp(LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.spouseEmailOptionLabel),
      },
      {
        value: FormFieldNames.spousePhone,
        label: tp(LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.spousePhoneOptionLabel),
      },
    ],
    [tp],
  );

  const {
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    user,
    quietUserRefetch,
  } = useRootContext();

  const { updateUserInfo } = useUpdateUserInfo();
  const { sendConsentLinkToSpouse } = useSendConsentLinkToSpouse();

  const {
    applicationUserInfoId,
    email,
    phone,
    address,
    postCode,
    city,
    iban,
    purposeOfLoan,
    productId,
  } = formatUserToPurposeOfLoanPageDataFormat(user);

  const form = useForm<PurposeOfLoanFormSchema>({
    resolver: zodResolver(purposeOfLoanFormSchema),
    defaultValues: {
      [FormFieldNames.purposeOfLoan]:
        purposeOfLoan ?? PurposeOfLoanType.MARRIED_HOUSEHOLD,
      [FormFieldNames.spouseApplicationMethod]: '',
      [FormFieldNames.spouseEmail]: '',
      [FormFieldNames.spousePhone]: '',
    },
  });

  const purposeOfLoanValue = form.watch(FormFieldNames.purposeOfLoan);
  const spouseApplicationMethod = form.watch(
    FormFieldNames.spouseApplicationMethod,
  );

  const onSubmit = async ({
    purpose_of_loan,
    spouse_email,
    spouse_phone,
  }: PurposeOfLoanFormSchema) => {
    try {
      await updateUserInfo({
        application_user_info_id: applicationUserInfoId ?? 0,
        purpose_of_loan,
        email,
        phone,
        address,
        post_code: postCode,
        city,
        iban,
      });

      if (purpose_of_loan === PurposeOfLoanType.MARRIED_HOUSEHOLD) {
        await sendConsentLinkToSpouse({
          credit_account_id: productId,
          spouse_email,
          spouse_phone,
        });
      }

      await quietUserRefetch();

      await getPageUrlAndNavigate(true);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name={FormFieldNames.purposeOfLoan}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div
                    className={cn(
                      'space-y-2 rounded-2xl border border-solid border-neutral-200 transition-all duration-500 ease',
                      field.value === PurposeOfLoanType.MARRIED_HOUSEHOLD &&
                        'pb-4',
                    )}
                  >
                    <FormLabel
                      htmlFor={PurposeOfLoanType.MARRIED_HOUSEHOLD}
                      className="flex items-center space-x-[1rem] cursor-pointer p-4"
                    >
                      <RadioGroupItem
                        value={PurposeOfLoanType.MARRIED_HOUSEHOLD}
                        id={PurposeOfLoanType.MARRIED_HOUSEHOLD}
                      />
                      <Typography
                        variant="text-m"
                        className="font-semibold ml-4"
                      >
                        {tp(
                          LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.purposeFamilyOptionLabel,
                        )}
                      </Typography>
                    </FormLabel>
                    <div
                      className={`pl-9 mr-4 !mt-0 !mb-0 transition-all duration-500 ease ${
                        field.value === PurposeOfLoanType.MARRIED_HOUSEHOLD
                          ? 'max-h-[500px] opacity-100'
                          : 'max-h-0 opacity-0 overflow-hidden'
                      }`}
                    >
                      <Typography
                        variant="text-s"
                        className="text-neutral-500 mt-[1.125rem]"
                      >
                        {tp(
                          LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.spouseMotivationalDisclaimer,
                        )}
                      </Typography>

                      <FormSelectField
                        className="mt-4"
                        control={form.control}
                        name={FormFieldNames.spouseApplicationMethod}
                        label={tp(
                          LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.spouseApplicationMethodFieldLabel,
                        )}
                        options={spouseApplicationMethodOptions}
                        invalid={
                          !!form.formState.errors[
                            FormFieldNames.spouseApplicationMethod
                          ]
                        }
                        onValueChange={(value) => {
                          if (value === FormFieldNames.spouseEmail) {
                            form.resetField(FormFieldNames.spousePhone);
                          } else if (value === FormFieldNames.spousePhone) {
                            form.resetField(FormFieldNames.spouseEmail);
                          }
                        }}
                      />

                      <div
                        className={`space-y-2 transition-all duration-500 ease ${
                          spouseApplicationMethod
                            ? 'max-h-[500px] opacity-100'
                            : 'max-h-0 opacity-0 overflow-hidden'
                        }`}
                      >
                        {spouseApplicationMethod ===
                        FormFieldNames.spouseEmail ? (
                          <FormInputField
                            className="mt-2"
                            control={form.control}
                            name={FormFieldNames.spouseEmail}
                            label={tp(
                              LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.spouseEmailFieldLabel,
                            )}
                            invalid={
                              !!form.formState.errors[
                                FormFieldNames.spouseEmail
                              ]
                            }
                          />
                        ) : null}

                        {spouseApplicationMethod ===
                        FormFieldNames.spousePhone ? (
                          <FormPhoneField
                            className="mt-2"
                            control={form.control}
                            name={FormFieldNames.spousePhone}
                            label={tp(
                              LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.spousePhoneFieldLabel,
                            )}
                            invalid={
                              !!form.formState.errors[
                                FormFieldNames.spousePhone
                              ]
                            }
                          />
                        ) : null}
                      </div>
                    </div>
                  </div>
                  <FormLabel
                    htmlFor={PurposeOfLoanType.MARRIED_PERSONAL}
                    className="flex items-center space-x-[1rem] p-4 rounded-2xl border border-solid border-neutral-200 cursor-pointer"
                  >
                    <RadioGroupItem
                      value={PurposeOfLoanType.MARRIED_PERSONAL}
                      id={PurposeOfLoanType.MARRIED_PERSONAL}
                    />
                    <Typography variant="text-m" className="font-semibold ml-4">
                      {tp(
                        LOCIZE_LOAN_TYPE_TRANSLATION_KEYS.purposePersonalOptionLabel,
                      )}
                    </Typography>
                  </FormLabel>
                </RadioGroup>
              </FormControl>
            </FormItem>
          )}
        />

        <Button
          fullWidth
          className="mt-12"
          disabled={
            !(
              purposeOfLoanValue === PurposeOfLoanType.MARRIED_HOUSEHOLD ||
              purposeOfLoanValue === PurposeOfLoanType.MARRIED_PERSONAL
            ) ||
            (!form.formState.isSubmitting && pageUrlAndNavigationProcessing)
          }
          loading={form.formState.isSubmitting}
          type="submit"
        >
          {t(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>
        <Button
          fullWidth
          className="mt-4"
          variant="white"
          loading={
            !form.formState.isSubmitting && pageUrlAndNavigationProcessing
          }
          disabled={form.formState.isSubmitting}
          onClick={() => {
            getPageUrlAndNavigate(false);
          }}
        >
          {t(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
        </Button>
      </form>
    </Form>
  );
};

export default Page;
