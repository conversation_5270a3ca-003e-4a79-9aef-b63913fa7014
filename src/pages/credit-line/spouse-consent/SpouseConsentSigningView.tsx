import {
  AppSigningMethods,
  Form<PERSON>ieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS,
  LocizeNamespaces,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import { FormPaymentMethodField } from 'components/form/form-payment-method-field';
import { FormPhoneField } from 'components/form/form-phone-field';
import { FormSelectField } from 'components/form/form-select-field';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { Skeleton } from 'components/ui/skeleton';
import { useSpouseConsentPageContext } from 'context/credit-line';
import { isLtRegion } from 'environment';
import type { SpouseConsentPageFormType } from 'hooks/page-logic/credit-line/use-spouse-consent-page-logic';
import type { AnyObject } from 'models';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

export const SpouseConsentSigningView = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.spouseConsent);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    form,
    onFormSubmit,
    spouseConsentFormValidationErrors,
    userName,
    spouseConsentSigningMethodSelectOptions,
    banklinkOptions,
    spouseConsentEmploymentDateOptions,
  } = useSpouseConsentPageContext();

  const selectedSigningMethod = form.watch(FormFieldNames.spouseSigningMethod);
  const selectedPaymentMethod = form.watch(FormFieldNames.paymentMethodKey);
  const spouseOverdueDebt = form.watch(FormFieldNames.spouseOverdueDebt);
  const isOverdueDebtChecked =
    !!spouseOverdueDebt ||
    spouseConsentFormValidationErrors[FormFieldNames.spouseOverdueDebt];

  return (
    <div>
      <Typography variant="xxs" tag="div">
        <AppLocalizationComponent
          locizeKey={
            LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.spouseConsentHeading
          }
          t={t}
          values={{
            userName: userName,
          }}
        />
      </Typography>

      <Typography className="mt-4">
        {t(LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.spouseConsentDescription)}
      </Typography>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onFormSubmit)}
          className="space-y-2 mt-8"
        >
          <FormNumberInputField
            name={FormFieldNames.spouseNetIncomeMonthly}
            control={form.control}
            label={t(
              isLtRegion
                ? LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.netIncomeLabelLt
                : LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.netIncomeLabel,
            )}
            info={t(
              isLtRegion
                ? LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.netIncomeTooltipLabelLt
                : LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.netIncomeTooltipLabel,
            )}
            suffix="€"
            disabled={form.formState.isSubmitting}
            invalid={
              !!spouseConsentFormValidationErrors[
                FormFieldNames.spouseNetIncomeMonthly
              ]
            }
          />
          <FormNumberInputField<SpouseConsentPageFormType>
            name={FormFieldNames.spouseExpenditureMonthly}
            control={form.control}
            label={t(LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.expenditureLabel)}
            info={t(
              LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.expenditureTooltipLabel,
            )}
            suffix="€"
            disabled={form.formState.isSubmitting}
            invalid={
              !!spouseConsentFormValidationErrors[
                FormFieldNames.spouseExpenditureMonthly
              ]
            }
          />
          <FormNumberInputField
            name={FormFieldNames.spouseMonthlyLivingExpenses}
            control={form.control}
            label={t(
              LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.livingExpensesPerMonthLabel,
            )}
            info={t(
              LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.livingExpensesPerMonthTooltipLabel,
            )}
            suffix="€"
            disabled={form.formState.isSubmitting}
            invalid={
              !!spouseConsentFormValidationErrors[
                FormFieldNames.spouseMonthlyLivingExpenses
              ]
            }
          />
          <CheckboxWrapper
            className="!my-8"
            checked={isOverdueDebtChecked}
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(FormFieldNames.spouseOverdueDebt, null);
              }
            }}
            label={t(
              LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.overdueDebtCheckboxLabel,
            )}
            info={t(
              LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.overdueDebtTooltipLabel,
            )}
            disabled={form.formState.isSubmitting}
            invalid={
              spouseConsentFormValidationErrors[
                FormFieldNames.spouseOverdueDebt
              ]
            }
          >
            <FormNumberInputField<SpouseConsentPageFormType>
              control={form.control}
              name={FormFieldNames.spouseOverdueDebt}
              label={t(LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.overdueDebtLabel)}
              disabled={form.formState.isSubmitting}
              invalid={
                spouseConsentFormValidationErrors[
                  FormFieldNames.spouseOverdueDebt
                ]
              }
              suffix={'€'}
            />
          </CheckboxWrapper>
          <FormSelectField<SpouseConsentPageFormType>
            control={form.control}
            name={FormFieldNames.spouseEmploymentDate}
            label={t(
              LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.employmentDateLabel,
            )}
            options={spouseConsentEmploymentDateOptions}
            disabled={form.formState.isSubmitting}
            invalid={
              spouseConsentFormValidationErrors[
                FormFieldNames.spouseEmploymentDate
              ]
            }
          />

          <FormSelectField<SpouseConsentPageFormType>
            control={form.control}
            name={FormFieldNames.spouseSigningMethod}
            label={t(
              LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.chooseSigningMethodsLabel,
            )}
            options={spouseConsentSigningMethodSelectOptions}
            disabled={form.formState.isSubmitting}
            invalid={
              spouseConsentFormValidationErrors[
                FormFieldNames.spouseSigningMethod
              ]
            }
          />
          <div
            className={cn(
              selectedSigningMethod === AppSigningMethods.mobileId
                ? 'max-h-[500px] opacity-100'
                : 'max-h-0 opacity-0 overflow-hidden',
            )}
          >
            <FormPhoneField<SpouseConsentPageFormType>
              control={form.control}
              name={FormFieldNames.spousePhone}
              label={t(LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.phoneFieldLabel)}
              disabled={form.formState.isSubmitting}
              invalid={spouseConsentFormValidationErrors[FormFieldNames.phone]}
            />
          </div>

          <div
            className={cn(
              selectedSigningMethod === AppSigningMethods.banklink
                ? 'max-h-[500px] opacity-100'
                : 'max-h-0 opacity-0 overflow-hidden',
            )}
          >
            <FormPaymentMethodField<SpouseConsentPageFormType>
              containerClassName={cn(
                '!-mt-2',
                !banklinkOptions?.length && 'hidden',
              )}
              control={form.control}
              name={FormFieldNames.paymentMethodKey}
              options={banklinkOptions}
              selectedValue={selectedPaymentMethod}
              invalid={
                spouseConsentFormValidationErrors[
                  FormFieldNames.paymentMethodKey
                ]
              }
              disabled={form.formState.isSubmitting}
            />
            <div
              className={cn(
                '!mt-[1.25rem] flex flex-wrap justify-center items-center gap-4',
                banklinkOptions?.length && 'hidden',
              )}
            >
              <Skeleton className="animate-pulse-opacity w-[7.628rem] h-12" />
              <Skeleton className="animate-pulse-opacity w-[7.628rem] h-12" />
              <Skeleton className="animate-pulse-opacity w-[7.628rem] h-12" />
              <Skeleton className="animate-pulse-opacity w-[7.628rem] h-12" />
              <Skeleton className="animate-pulse-opacity w-[7.628rem] h-12" />
            </div>
          </div>

          <Typography
            variant="text-s"
            tag="div"
            className="text-neutral-500 text-center !mt-10"
          >
            <AppLocalizationComponent
              components={{
                site_link: (
                  <AppExternalLink
                    className="underline hover:text-primary-brand02"
                    to={
                      (REDIRECT_URLS.termsPageUrs as AnyObject)[i18n.language]
                    }
                  />
                ),
              }}
              locizeKey={
                LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS.signingDisclaimer
              }
              t={t}
            />
          </Typography>

          <Button
            disabled={form.formState.isSubmitting || !form.formState.isValid}
            fullWidth
            className="!mt-6"
            loading={form.formState.isSubmitting}
            type="submit"
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.signLabel)}
          </Button>
        </form>
      </Form>
    </div>
  );
};
