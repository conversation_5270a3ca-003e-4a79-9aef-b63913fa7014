import type { AppLanguages } from 'app-constants';
import { lazy, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

import { useGetCreditLineInfoBanners } from '../hooks';
import { CreditLineInfoHeader } from './CreditLineInfoHeader';
import { CreditLineProductCarousel } from './CreditLineProductCarousel';

const CreditLineInfoDialog = lazy(() =>
  import('./CreditLineInfoDialog').then((module) => ({
    default: module.CreditLineInfoDialog,
  })),
);

export const CreditLineInfo = ({ className }: { className?: string }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { i18n } = useTranslation();

  const banners = useGetCreditLineInfoBanners({
    language: i18n.language as AppLanguages,
  });

  if (banners.length === 0) return null;

  return (
    <>
      <CreditLineInfoDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
      />
      <div
        className={cn(
          'flex flex-col gap-[1.5rem] pt-6 mb-12 md:p-0 w-[calc(100%-1rem)] md:w-[calc(100%-3rem)] lg:m-auto lg:w-full',
          className,
        )}
      >
        <CreditLineInfoHeader />
        <CreditLineProductCarousel />
      </div>
    </>
  );
};
