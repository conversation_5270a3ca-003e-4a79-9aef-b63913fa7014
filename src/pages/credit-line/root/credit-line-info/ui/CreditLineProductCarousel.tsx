import { type AppLanguages, LocizeNamespaces } from 'app-constants';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from 'components/ui/carousel';
import Autoplay from 'embla-carousel-autoplay';
import Fade from 'embla-carousel-fade';
import { useIsMobileView } from 'hooks/system';
import { useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';

import { useGetCreditLineInfoBanners } from '../hooks';

export const CreditLineProductCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [autoplayEnabled, setAutoplayEnabled] = useState(true);
  const { i18n } = useTranslation();

  const banners = useGetCreditLineInfoBanners({
    language: i18n.language as AppLanguages,
  });

  const { t } = useTranslation(LocizeNamespaces.creditLine);

  const isMobileView = useIsMobileView();

  if (banners.length === 0) return null;

  return (
    <Carousel
      opts={{
        loop: true,
        duration: 30,
      }}
      setApi={setApi}
      onMouseLeave={() => api?.plugins().autoplay.play()}
      plugins={[
        ...(autoplayEnabled
          ? [
              Autoplay({
                delay: 5000,
                stopOnMouseEnter: true,
                playOnInit: true,
              }),
            ]
          : []),
        Fade(),
      ]}
    >
      <div className="relative w-full">
        <CarouselContent isInteractive>
          {banners.map((banner) => (
            <CarouselItem key={banner.id}>
              <div>
                <div
                  className="flex items-center h-[19rem] lg:h-[25rem] w-full rounded-2xl cursor-pointer bg-center bg-cover"
                  key={banner.id}
                  style={{
                    backgroundImage: banner.img
                      ? `url(${banner.img})`
                      : undefined,
                  }}
                >
                  <div className="absolute top-6 left-9 w-7 h-7 bg-white rounded-full flex items-center justify-center">
                    <div className="text-black text-base font-bold">
                      {banner.id}
                    </div>
                  </div>
                </div>
                <div className="text-[1.25rem] font-normal pt-6 text-center">
                  <Trans
                    className="text-[1.25rem] font-normal pt-6 text-center"
                    i18nKey={banner.description}
                    t={t}
                  />
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        {banners.length > 1 && !isMobileView && (
          <>
            <CarouselPrevious
              onClick={() => {
                setAutoplayEnabled(false);
                api?.scrollPrev();
              }}
              className="p-1 top-[9.5rem] lg:top-[12.5rem]"
              variant="transparent"
            />
            <CarouselNext
              onClick={() => {
                setAutoplayEnabled(false);
                api?.scrollNext();
              }}
              className="p-1 top-[9.5rem] lg:top-[12.5rem]"
              variant="transparent"
            />
          </>
        )}
      </div>
    </Carousel>
  );
};
