import {
  Currencies,
  FormFieldNames,
  LocalStorageKeys,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { AppLocalizationComponent } from 'components/app-localization-component';
import { Notification } from 'components/notification';
import { Typography } from 'components/typography';
import { useRootContext } from 'context/root';
import { useGetPageAttributesSuspense, useUpdateCreditAccount } from 'hooks';
import SmearIcon from 'icons/smear.svg?react';
import { lazy, Suspense, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { convertPageAttributeNamesToObject } from 'services';
import { getFromStorage, removeFromStorage } from 'services/storage-service';
import { formatNumber } from 'utils';
import { cn } from 'utils/tailwind';

import type { LimitFormType } from './schema';

const LimitDecreaseModalLazy = lazy(() =>
  import('./LimitDecreaseModal').then((mod) => ({
    default: mod.LimitDecreaseModal,
  })),
);

export const ApprovedCreditLimitBlock = () => {
  const { t } = useTranslation(LocizeNamespaces.signing);
  const { updateCreditAccount } = useUpdateCreditAccount();
  const { user, quietUserRefetch } = useRootContext();
  const { pageAttributes } = useGetPageAttributesSuspense();

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const isAllowedToChangeCreditLimit =
    visiblePageAttributes[PageAttributeNames.signingPageAmountSlider];

  const [isUpdatingCreditLimit, setIsUpdatingCreditLimit] = useState(false);

  const creditAccount = user?.credit_accounts?.[0];

  if (!creditAccount) {
    throw new Error(
      'Credit account is not found while rendering approved credit limit block',
    );
  }

  const maxCreditAmount = creditAccount?.potential_credit_limit ?? 0;
  const creditLineChosenAmount = getFromStorage(
    LocalStorageKeys.creditLineChosenAmount,
  );
  const isAvailableCreditAmount = creditLineChosenAmount <= maxCreditAmount;
  const selectedLimit =
    isAllowedToChangeCreditLimit && isAvailableCreditAmount
      ? creditLineChosenAmount || creditAccount.credit_limit || maxCreditAmount // this fix is for https://estoas.atlassian.net/browse/PPFV-529
      : maxCreditAmount;
  const [isOpenDialog, setIsOpenDialog] = useState(false);

  const updateCreditLimit = async (limit: number) => {
    setIsUpdatingCreditLimit(true);
    try {
      await updateCreditAccount({
        credit_account_id: creditAccount.id,
        credit_limit: limit,
      });

      await quietUserRefetch();

      removeFromStorage(LocalStorageKeys.creditLineChosenAmount);
    } catch (error) {
      console.error(error);
    } finally {
      setIsUpdatingCreditLimit(false);
    }
  };

  const onConfirm = async (values: LimitFormType) => {
    await updateCreditLimit(values[FormFieldNames.creditLimit]);

    setIsOpenDialog(false);
  };

  return (
    <div className="flex flex-col items-center py-8">
      <Typography variant="s">
        {t(LOCIZE_SIGNING_TRANSLATION_KEYS.approvedTitle, {
          userName: user?.profile?.first_name,
        })}
      </Typography>
      <Typography className="mt-4" variant="text-l">
        {t(LOCIZE_SIGNING_TRANSLATION_KEYS.approvedDescription)}
      </Typography>
      <div className="flex flex-col items-center justify-center w-full relative mt-8 pb-4">
        <Typography className="relative z-10 " variant="xl">
          {formatNumber({
            value: selectedLimit,
          })}{' '}
          {Currencies.euro}
        </Typography>
        <SmearIcon className="w-full absolute -bottom-1 left-0 " />
      </div>

      {isAllowedToChangeCreditLimit ? (
        <div>
          {selectedLimit === maxCreditAmount ? (
            <Typography
              variant="text-s"
              affects="link"
              className="mt-8 cursor-pointer hover:text-primary-brand02"
              onClick={() => setIsOpenDialog(true)}
            >
              {t(LOCIZE_SIGNING_TRANSLATION_KEYS.decreaseLimitButtonLabel)}
            </Typography>
          ) : (
            <Notification className="mt-8">
              <AppLocalizationComponent
                components={{
                  button: (
                    <div
                      onClick={() => {
                        if (isUpdatingCreditLimit) {
                          return;
                        }
                        updateCreditLimit(maxCreditAmount);
                      }}
                      className={cn(
                        'text-primary-brand02 underline underline-from-font hover:text-primary-brand02 inline cursor-pointer hover:opacity-80',
                        isUpdatingCreditLimit &&
                          'cursor-progress text-neutral-200 no-underline hover:text-neutral-200 hover:opacity-100',
                      )}
                    />
                  ),
                }}
                values={{
                  limit: formatNumber({
                    value: maxCreditAmount,
                  }),
                }}
                locizeKey={
                  LOCIZE_SIGNING_TRANSLATION_KEYS.increaseLimitNotificationText
                }
                t={t}
              />
            </Notification>
          )}
        </div>
      ) : null}

      <Suspense fallback={null}>
        <LimitDecreaseModalLazy
          onConfirm={onConfirm}
          currentLimit={creditAccount.credit_limit}
          isOpen={isOpenDialog}
          onOpenChange={setIsOpenDialog}
        />
      </Suspense>
    </div>
  );
};
