import {
  FormFieldNames,
  INCOME_VERIFICATION_ACCEPT_FILE_TYPES,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import {
  FileUploader,
  UploadedFiles,
} from 'components/file-uploader/FileUploader';
import { FormSelectField } from 'components/form/form-select-field';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import {
  type OccupationFormType,
  useIncomeVerificationPageLogic,
} from 'hooks/page-logic/credit-line';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Skeleton from 'react-loading-skeleton';
import {
  isOccupationCategory,
  isOccupationCategoryNoAdditionalDocumentsNeeded,
  occupationDocumentsByCategory,
} from 'services/occupation-service';
import { v4 as uuid } from 'uuid';

const ACCEPTED_FILE_TYPES = INCOME_VERIFICATION_ACCEPT_FILE_TYPES.join(', ');

const CreditLineOccupationPage = () => {
  const [uploadedStatements, setUploadedStatements] = useState<File[]>([]);

  const { t } = useTranslation(LocizeNamespaces.incomeVerification);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    form,
    occupationCategoryOptions,
    onOccupationFormSubmit,
    onDocumentsUploaded,
    onDocumentRemoved,
    userIncomeLiabilityDocuments,
    userIncomeLiabilityDocumentsLoading,
    pageUrlAndNavigationProcessing,
    getPageUrlAndNavigate,
    documentIdToDelete,
    isUploading,
    incomeVerificationPageDataLoading,
  } = useIncomeVerificationPageLogic();

  const selectedOccupation = form.watch(FormFieldNames.occupationCategory);

  const noAdditionalDocumentsNeeded =
    isOccupationCategoryNoAdditionalDocumentsNeeded(selectedOccupation) ||
    !selectedOccupation;

  const renderOccupationNeededDocuments = () => {
    if (!selectedOccupation || !isOccupationCategory(selectedOccupation))
      return null;

    return occupationDocumentsByCategory[selectedOccupation]?.map(
      (document: string) => (
        <div key={uuid()} className="flex flex-row gap-2 w-full">
          <div className="w-[0.3rem] min-w-[0.3rem] mt-2 min-h-[0.3rem] h-[0.3rem] bg-primary-brand02 rounded-full" />
          <Typography variant="text-s">{t(document)}</Typography>
        </div>
      ),
    );
  };

  const onRemoveDocument = (id: number) => {
    onDocumentRemoved(id);
  };

  const onUploadDocument = (files: File[]) => {
    setUploadedStatements(files);
    onDocumentsUploaded(files);
  };

  return (
    <div className="flex flex-col gap-8 w-full h-full">
      <Typography variant="xxs">
        {t(LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.occupationTitle)}
      </Typography>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onOccupationFormSubmit)}>
          {incomeVerificationPageDataLoading ? (
            <Skeleton className="h-12 w-full" />
          ) : (
            <FormSelectField<OccupationFormType>
              control={form.control}
              name={FormFieldNames.occupationCategory}
              label={t(
                LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.occupationLabel,
              )}
              options={occupationCategoryOptions}
            />
          )}

          <section className="flex flex-col mr-2 ml-2">
            <div className="flex flex-col gap-4 w-full mt-6 mb-6">
              <Typography variant="text-s" affects="semibold" className="">
                {tc(LOCIZE_COMMON_TRANSLATION_KEYS.provideFollowingDocuments)}
              </Typography>

              {incomeVerificationPageDataLoading ? (
                <Skeleton className="h-12 w-full" />
              ) : (
                renderOccupationNeededDocuments()
              )}
            </div>

            {!noAdditionalDocumentsNeeded && (
              <FileUploader
                acceptedFileTypes={ACCEPTED_FILE_TYPES}
                files={uploadedStatements}
                isLoading={isUploading}
                onFilesChange={(files) => {
                  onUploadDocument(files);
                }}
                uploadedFiles={
                  <UploadedFiles
                    onRemoveDocument={onRemoveDocument}
                    isUploading={isUploading}
                    documentIdToDelete={documentIdToDelete}
                    uploadedFiles={userIncomeLiabilityDocuments}
                    isLoading={userIncomeLiabilityDocumentsLoading}
                  />
                }
              />
            )}
          </section>
          <Button
            className="mt-12"
            fullWidth
            type="submit"
            disabled={
              !form.formState.isSubmitting && pageUrlAndNavigationProcessing
            }
            loading={form.formState.isSubmitting}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
          </Button>

          <Button
            fullWidth
            className="mt-2"
            variant="white"
            loading={
              !form.formState.isSubmitting && pageUrlAndNavigationProcessing
            }
            disabled={form.formState.isSubmitting}
            onClick={() => {
              getPageUrlAndNavigate(false);
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default CreditLineOccupationPage;
