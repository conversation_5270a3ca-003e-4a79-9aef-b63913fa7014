import {
  APP_DISCLAIMER_TYPES,
  AppLoginMethods,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { AppDisclaimer } from 'components';
import { DisclaimerNotification } from 'components/notification';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useLoginPageContext } from 'context/credit-line';
import { useTranslation } from 'react-i18next';

import styles from './Login.module.scss';
import { LOGIN_FORMS_MAP } from './login-forms.map';

export const LoginView = () => {
  const {
    loginButtons,
    selectedLoginMethod,
    isLoginFlowWithError,
    visiblePageAttributes,
  } = useLoginPageContext();

  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: te } = useTranslation(LocizeNamespaces.errors);

  const loginPageHeading = t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginPageTitle);
  const errorTitle = te(
    LOCIZE_ERRORS_TRANSLATION_KEYS.authenticationErrorLabel,
  );

  const renderLoginMethodButtons = () =>
    loginButtons?.map(({ label, method, ...props }) => (
      <Button {...props} key={method}>
        {label}
      </Button>
    ));

  const LoginForm = LOGIN_FORMS_MAP[selectedLoginMethod];

  return (
    <>
      <Typography variant="xs" className="text-center">
        {loginPageHeading}
      </Typography>
      <div className={styles.buttons}>{renderLoginMethodButtons()}</div>

      {visiblePageAttributes[PageAttributeNames.smartIdFullDisclaimer] &&
        selectedLoginMethod === AppLoginMethods.smartId && (
          <DisclaimerNotification className="mt-6">
            {t(LOCIZE_LOGIN_TRANSLATION_KEYS.smartIdFullVersionDisclaimer)}
          </DisclaimerNotification>
        )}

      <AppDisclaimer
        title={errorTitle}
        type={APP_DISCLAIMER_TYPES.error}
        visible={isLoginFlowWithError}
      />

      <div className={styles.form}>
        <LoginForm />
      </div>
    </>
  );
};
