import type { SuspenseQueryHookOptions } from '@apollo/client';
import { NetworkStatus } from '@apollo/client';
import {
  type PageAttributesQuery,
  type PageAttributesQueryVariables,
  usePageAttributesSuspenseQuery,
} from 'api/purchase-flow/generated';
import { AppApiVersions } from 'app-constants';
import { useRef } from 'react';
import { useLocation } from 'react-router-dom';

const routeCache = new Map<string, boolean>();

export const useGetPageAttributesSuspense = (
  options?: SuspenseQueryHookOptions<
    PageAttributesQuery,
    PageAttributesQueryVariables
  >,
) => {
  const skipQuery = useRef(false);
  const location = useLocation();
  const url = `${location.pathname}${location.search}`;

  const { data, error, networkStatus } = usePageAttributesSuspenseQuery({
    variables: { url },
    context: { apiVersion: AppApiVersions.purchaseFlow },
    skip: skipQuery.current,
    ...options,
  });

  if (routeCache.has(url)) {
    skipQuery.current = true;
  } else {
    routeCache.set(url, true);
  }

  const attributes = data?.page_attributes?.attributes;

  return {
    pageAttributes: attributes ? JSON.parse(attributes).fields_to_show : [],
    pageAttributesError: error,
    pageAttributesLoading: networkStatus === NetworkStatus.loading,
  };
};
