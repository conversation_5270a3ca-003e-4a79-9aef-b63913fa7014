import type { CreditAccount } from 'api/core/generated';
import { REDIRECT_URLS } from 'app-constants';
import { useRootContext } from 'context/root';
import { useForwardLoan } from 'hooks/use-forward-loan';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspence';
import type { CreditLineRejectPageLogic } from 'models';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { convertPageAttributeNamesToObject } from 'services';
import { isAppLanguage } from 'utils';

export const useRejectPageLogic = (): CreditLineRejectPageLogic => {
  const { i18n } = useTranslation();
  const { user } = useRootContext();
  const { pageAttributes } = useGetPageAttributesSuspense();
  const { getForwardLoanLink } = useForwardLoan();

  const [isForwardLoadLinkProcessing, setForwardLoadLinkProcessing] =
    useState(false);
  const [isRedirectingToCustomerProfile, setIsRedirectingToCustomerProfile] =
    useState(false);
  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const onGoToFAQButtonClick = (): void => {
    if (isAppLanguage(i18n.language)) {
      window.open(
        REDIRECT_URLS.creditLineFrequentlyAskedQuestionsPageUrls[i18n.language],
        '_blank',
      );
    } else {
      throw new Error('Unsupported language');
    }
  };

  const onEstoAccountButtonClick = (): void => {
    setIsRedirectingToCustomerProfile(true);
    window.location.href = REDIRECT_URLS.newCustomerProfile;
  };

  const onForwardLoanClick = () => {
    setForwardLoadLinkProcessing(true);

    const creditAccount = (user?.credit_accounts ?? [{}])[0] as CreditAccount;

    getForwardLoanLink({ credit_account_id: creditAccount.id })
      .then(({ data }) => {
        if (data?.url) {
          window.location.href = data.url;
        }
      })
      .catch(() => {
        setForwardLoadLinkProcessing(false);
      });
  };

  return useMemo(
    () => ({
      onGoToFAQButtonClick,
      visiblePageAttributes,
      onEstoAccountButtonClick,
      isForwardLoadLinkProcessing,
      isRedirectingToCustomerProfile,
      onForwardLoanClick,
    }),
    [
      onGoToFAQButtonClick,
      visiblePageAttributes,
      onEstoAccountButtonClick,
      isForwardLoadLinkProcessing,
      isRedirectingToCustomerProfile,
      onForwardLoanClick,
    ],
  );
};
