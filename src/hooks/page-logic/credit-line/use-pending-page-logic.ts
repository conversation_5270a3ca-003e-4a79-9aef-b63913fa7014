import type { CreditAccount } from 'api/core/generated';
import {
  AppRoutePaths,
  CreditLineRoutePaths,
  REDIRECT_URLS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspense';
import { useRudderStack } from 'hooks/use-rudderstack';
import { useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { convertPageAttributeNamesToObject } from 'services';

export const usePendingPageLogic = () => {
  const { ruderStackEvents } = useRudderStack();
  const { search } = useLocation();
  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing, user } =
    useRootContext();
  const [isRedirectingToCustomerProfile, setIsRedirectingToCustomerProfile] =
    useState(false);
  const [isRedirectingToPaymentPage, setIsRedirectingToPaymentPage] =
    useState(false);

  const { pageAttributes } = useGetPageAttributesSuspense();

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const unpaidInvoiceAmount = user?.unpaid_invoice_amount;
  const referenceKey = user?.reference_key;

  const onEstoAccountButtonClick = () => {
    setIsRedirectingToCustomerProfile(true);
    window.location.href = REDIRECT_URLS.newCustomerProfile;
  };

  const sendAnalyticsEvent = () => {
    const creditAccount = (user?.credit_accounts ?? [{}])[0] as CreditAccount;

    if (!creditAccount?.id) {
      throw new Error('Credit account ID is missing');
    }

    ruderStackEvents.verifyInformation({
      creditAccountId: creditAccount.id,
    });
  };

  const onCheckIncomeButtonClick = async () => {
    sendAnalyticsEvent();
    getPageUrlAndNavigate(null, {
      customCurrentPageUrl: `/${AppRoutePaths.CREDIT_LINE}/${CreditLineRoutePaths.CONTACT_EXTRA}${search}`,
    });
  };

  const onPayDebtButtonClick = () => {
    if (!referenceKey) {
      throw new Error('Reference key is missing');
    }
    setIsRedirectingToPaymentPage(true);
    const paymentUrl = `${REDIRECT_URLS.newCustomerProfileInvoicePayment}?referenceKey=${referenceKey}`;
    window.location.href = paymentUrl;
  };

  return {
    isRedirectingToCustomerProfile,
    isRedirectingToPaymentPage,
    visiblePageAttributes,
    debtAmount: unpaidInvoiceAmount,
    onPayDebtButtonClick,
    onEstoAccountButtonClick,
    onCheckIncomeButtonClick,
    pageUrlAndNavigationProcessing,
  };
};
