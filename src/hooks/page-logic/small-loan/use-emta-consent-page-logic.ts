import {
  AppRoutePaths,
  AppSearchParams,
  EMTA_STATUSES,
  IncomeSourcesCheckServices,
  LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS,
  LocizeNamespaces,
  SmallLoanRoutePaths,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetEmtaConsent } from 'hooks/use-get-emta-consent';
import { useEffectOnce } from 'hooks/utils';
import type { AnyObject, EmtaConsentPageLogic } from 'models';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useSearchParams } from 'react-router-dom';
import { replaceOrAddQueryParamsInUrl } from 'services';
import { v4 as uuid } from 'uuid';

export const useEmtaConsentPageLogic = (): EmtaConsentPageLogic => {
  const { t } = useTranslation(LocizeNamespaces.emtaConsent);
  const { search, pathname } = useLocation();
  const [searchParams] = useSearchParams();
  const emtaStatus = searchParams.get(AppSearchParams.emtaStatus) ?? '';

  const { getApplication } = useGetCurrentApplication({
    onCompleted: (data) => {
      requestEmtaConsent({
        application_id: data?.application?.id,
      });
    },
  });

  const [
    selectedIncomeSourceCheckService,
    setSelectedIncomeSourceCheckService,
  ] = useState(IncomeSourcesCheckServices.emta);

  const { user, getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();

  const {
    getEmtaConsent,
    startEmtaConsentPolling,
    stopEmtaConsentPolling,
    emtaConsent,
  } = useGetEmtaConsent();

  const incomeSourcesCheckServicesOptions = [
    {
      id: uuid(),
      onClick: () =>
        setSelectedIncomeSourceCheckService(IncomeSourcesCheckServices.emta),
      isActive:
        selectedIncomeSourceCheckService === IncomeSourcesCheckServices.emta,
      title: t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaConsentButtonTitle),
      description: t(
        LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaConsentButtonDescription,
      ),
    },
    {
      id: uuid(),
      onClick: () =>
        setSelectedIncomeSourceCheckService(
          IncomeSourcesCheckServices.accountScoring,
        ),
      isActive:
        selectedIncomeSourceCheckService ===
        IncomeSourcesCheckServices.accountScoring,
      title: t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.accountScoringButtonTitle),
      description: t(
        LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.accountScoringButtonDescription,
      ),
    },
  ];

  const onContinueButtonClick = () => {
    switch (selectedIncomeSourceCheckService) {
      case IncomeSourcesCheckServices.emta:
        if (emtaConsent?.url) {
          window.location.href = emtaConsent?.url;
        }
        break;
      case IncomeSourcesCheckServices.accountScoring:
        getPageUrlAndNavigate(null, {
          customCurrentPageUrl: `/${AppRoutePaths.SMALL_LOAN}/${SmallLoanRoutePaths.ACCOUNT_SCORING}${search}`,
        });
        break;
      default:
        break;
    }
  };

  const onEmtaRedirectButtonClick = () => {
    if (emtaConsent?.url) {
      window.location.href = emtaConsent?.url;
    }
  };

  const onAccountScoringRedirectButtonClick = () => {
    getPageUrlAndNavigate(null, {
      customCurrentPageUrl: `/${AppRoutePaths.SMALL_LOAN}/${SmallLoanRoutePaths.ACCOUNT_SCORING}${search}`,
    });
  };

  useEffectOnce(() => {
    getApplication();
  });

  const requestEmtaConsent = (productVariables: AnyObject) => {
    const returnUrl = replaceOrAddQueryParamsInUrl({
      returnWithBase: true,
      url: `${pathname}${search}`,
      params: {
        [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
      },
    });

    if (!user?.id) {
      throw new Error('User id is not defined');
    }

    getEmtaConsent({
      user_id: user.id,
      return_url: returnUrl,
      ...productVariables,
    });
  };

  useEffect(() => {
    if (emtaStatus === EMTA_STATUSES.loading) {
      const returnUrl = replaceOrAddQueryParamsInUrl({
        returnWithBase: true,
        url: `${pathname}${search}`,
        params: {
          [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
        },
      });

      if (!user?.id) {
        throw new Error('User id is not defined');
      }

      startEmtaConsentPolling({
        user_id: user.id,
        return_url: returnUrl,
      });
    }
  }, [emtaStatus]);

  useEffect(() => {
    if (emtaConsent?.valid_until) {
      const customCurrentPageUrl = replaceOrAddQueryParamsInUrl({
        returnWithBase: true,
        url: `${pathname}${search}`,
        params: {
          [AppSearchParams.emtaStatus]: EMTA_STATUSES.finished,
        },
      });

      stopEmtaConsentPolling();

      getPageUrlAndNavigate(true, { customCurrentPageUrl });
    }
  }, [emtaConsent?.valid_until]);

  return {
    onContinueButtonClick,
    onEmtaRedirectButtonClick,
    onAccountScoringRedirectButtonClick,
    incomeSourcesCheckServicesOptions,
    returnedFromEmtaService: emtaStatus === EMTA_STATUSES.loading,
    emtaConsentPageProcessing:
      !emtaConsent ||
      pageUrlAndNavigationProcessing ||
      emtaStatus === EMTA_STATUSES.loading,
  };
};
