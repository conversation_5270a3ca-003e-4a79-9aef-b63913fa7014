import { ContractType, UsersignInMethod } from 'api/core/generated';
import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  AppSearchParams,
  BANKLINK_PAYMENT_POLL_STATUSES,
  FormFieldNames,
  GoogleAnalyticsEvents,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  NEGATIVE_ELIGIBILITY_STATUSES,
  PageAttributeNames,
  PAYSERA_PAYMENT_STATUSES,
  PRICING_KEYS,
  PURCHASE_FLOW_LOG_ACTIONS,
  SigningPageViewTypes,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { coreApiUrl, onlyPasswordSigningEnabled } from 'environment';
import { useGetBanklinkPaymentStatus } from 'hooks/use-banklink-payment-status-poll';
import { useBanklinkSigningPoll } from 'hooks/use-banklink-signing-poll';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useGetCreditSettings } from 'hooks/use-get-credit-settings';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetPageAttributes } from 'hooks/use-get-page-attributes';
import { useGetPricing } from 'hooks/use-get-pricing';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useLogout } from 'hooks/use-logout';
import { useSignContractWithBanklink } from 'hooks/use-sign-contract-with-banklink';
import { useSignContractWithIdCard } from 'hooks/use-sign-contract-with-id-card';
import { useSignContractWithMobileId } from 'hooks/use-sign-contract-with-mobile-id';
import { useSignContractWithPassword } from 'hooks/use-sign-contract-with-password';
import { useSignContractWithSmartId } from 'hooks/use-sign-contract-with-smart-id';
import { useUpdateApplicationCampaign } from 'hooks/use-update-application-campaign-mutation';
import { useUpdateApplicationPayoutMethod } from 'hooks/use-update-application-payout-method';
import { useUpdateApplicationUserInfoSigning } from 'hooks/use-update-application-user-info-signing';
import { useEffectOnce, useLocalStorage } from 'hooks/utils';
import { PayoutMethod, type SmallLoanSigningPageLogic } from 'models';
import { useEffect, useMemo, useState } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  createSearchParams,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatApplicationToSigningPageDataFormat,
  formatPricingDataToObject,
  getFilteredUrlSearchParamsObject,
  getSmallLoanAmountFromStorage,
  loadAndInitializeIdCardScripts,
  removeFromStorage,
  removeSmallLoanAmountFromStorage,
  setToStorage,
} from 'services';

declare let iSignApplet: {
  init: (options: Record<string, unknown>) => void;
  getCertificate: (options: Record<string, unknown>) => void;
  setHashAlgorithm: (algorithm: string) => void;
  sign: (
    dtbs: string,
    dtbsHash: string,
    callback: (signedHash: string) => void,
  ) => void;
};

export const useSigningPageLogic = (): SmallLoanSigningPageLogic => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const payseraSigningPaymentStatus = searchParams.get(
    AppSearchParams.payseraSigningPaymentStatus,
  );
  const { t: te, i18n } = useTranslation(LocizeNamespaces.errors);
  const {
    user,
    getUser,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    trackGoogleAnalyticsEvent,
  } = useRootContext();
  const { logOut } = useLogout();
  const { logAction } = useLogApplicationAction();
  const { signContractWithPassword, signContractWithPasswordProcessing } =
    useSignContractWithPassword();
  const {
    signContractWithSmartId,
    prepareSmartIdContractSignature,
    smartIdContractSignaturePreparationChallenge,
    smartIdContractSignaturePreparationLoading,
    smartIdContractSignaturePreparationError,
  } = useSignContractWithSmartId();
  const { signContractWithIdCard, prepareIdCardContractSignature } =
    useSignContractWithIdCard();
  const {
    signContractWithMobileId,
    prepareMobileIdContractSignature,
    mobileIdContractSignaturePreparationChallenge,
    mobileIdContractSignaturePreparationLoading,
    mobileIdContractSignaturePreparationError,
  } = useSignContractWithMobileId();
  const {
    prepareBanklinkContractSignature,
    banklinkSigningProcessing,
    banklinkSigningAcceptUrl,
    banklinkSigningCancelUrl,
  } = useSignContractWithBanklink();
  const {
    banklinkPaymentStatus,
    startBanklinkPaymentStatusPolling,
    stopBanklinkPaymentStatusPolling,
  } = useGetBanklinkPaymentStatus();
  const {
    isBanklinkSigningPollSuccess,
    startBanklinkSigningStatusPolling,
    stopBanklinkSigningStatusPolling,
  } = useBanklinkSigningPoll();
  const { pageAttributes, pageAttributesLoading, getPageAttributes } =
    useGetPageAttributes();
  const { getBanklinks, banklinks } = useGetBanks();
  const { getPricingData, pricingData, pricingDataLoading } = useGetPricing();
  const { getCreditSettings, creditSettings } = useGetCreditSettings();
  const { updateApplicationCampaign, applicationCampaignUpdating } =
    useUpdateApplicationCampaign();
  const { updateApplicationPaymentMethod, isApplicationPayoutMethodUpdating } =
    useUpdateApplicationPayoutMethod();
  const {
    application,
    applicationReferenceKey,
    applicationLoading,
    getApplication,
  } = useGetCurrentApplication({
    onCompleted: (data) => {
      if (data.application?.id) {
        const paymentLeaveEnabled = Boolean(
          application.campaign?.payment_leave_enabled,
        );

        setIsPaymentLeaveEnabled(paymentLeaveEnabled);

        if (data.application.credit_info) {
          const { net_total, down_payment } = data.application.credit_info;
          getCreditSettings({
            net_total,
            down_payment,
            application_id: data.application.id,
          });
        }

        if (
          NEGATIVE_ELIGIBILITY_STATUSES.includes(
            data.application.simple_eligibility_status,
          )
        ) {
          logAction({
            productId: data.application.id,
            action: PURCHASE_FLOW_LOG_ACTIONS.rejected,
          });
        } else {
          logAction({
            productId: data.application.id,
            action: PURCHASE_FLOW_LOG_ACTIONS.signingPage,
          });
        }
      }
    },
  });

  const {
    updateApplicationUserInfoSigning,
    applicationUserInfoSigningUpdating,
    applicationUserInfoSigningUpdateError,
  } = useUpdateApplicationUserInfoSigning();

  const { applicationUserInfoId, iban } = useMemo(
    () => formatApplicationToSigningPageDataFormat(application),
    [application],
  );

  const signingPageFormConfig = useMemo(
    () => ({
      defaultValues: {
        [FormFieldNames.iban]: iban ?? '',
      },
    }),
    [iban],
  );

  const [storedQueryParams, storeQueryParams] = useLocalStorage<
    Nullable<Record<string, string>>
  >(LocalStorageKeys.storedQueryParams, null);
  const [signingPageViewType, setSigningPageViewType] = useState(
    SigningPageViewTypes.signing,
  );
  const [isBanklinkOptionSelected, setIsBanklinkOptionSelected] =
    useState(false);
  const [
    signContractWithIDCardProcessing,
    setSignContractWithIDCardProcessing,
  ] = useState(false);

  const selectBanklinkOption = () => setIsBanklinkOptionSelected(true);
  const [isPaymentLeaveEnabled, setIsPaymentLeaveEnabled] = useState(false);
  const pricingDataWithKeys = formatPricingDataToObject(pricingData);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const signInMethod = application.user?.sign_in_method ?? null;

  const signingPageLoaded =
    Boolean(creditSettings) &&
    !pageUrlAndNavigationProcessing &&
    !pageAttributesLoading &&
    ((Boolean(application.id) && applicationLoading) || !applicationLoading);

  const userCanSignContract =
    application.is_test || signInMethod !== UsersignInMethod.PASSWORD;

  const contractLink = `${coreApiUrl}/application/${application.id}/download-contract?contract-type=${ContractType.SMALL_LOAN_SIGNED}`;

  const initialLoanAmount = getSmallLoanAmountFromStorage(application.id);

  const isInitialLoanAmountDisallowed =
    +application.requested_amount < Number(initialLoanAmount);

  const onPinConfirmationCancel = () => {
    setSigningPageViewType(SigningPageViewTypes.signing);
  };

  const onLogOutClick = () => {
    logOut().then(() => {
      getUser();
      getPageUrlAndNavigate();
    });
  };

  const logSuccessfulSigningAction = () => {
    // LOGGING ACTION

    if (application.id) {
      logAction({
        productId: application.id,
        action: PURCHASE_FLOW_LOG_ACTIONS.successfullySigned,
      });
    }
  };

  const executeSuccessfulSigningCallbacks = () => {
    removeSmallLoanAmountFromStorage(application.id);
    trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contractSigned);
    logSuccessfulSigningAction();
    getPageUrlAndNavigate(true);
  };

  const signAppWithPassword = () => {
    signContractWithPassword({
      application_id: application.id,
      contract_type: ContractType.SMALL_LOAN_SIGNED,
    }).then(() => {
      executeSuccessfulSigningCallbacks();
    });
  };

  const userInfoValidationErrors = extractValidationErrors(
    applicationUserInfoSigningUpdateError,
  );

  const prepareSigningAppWithSmartId = () => {
    prepareSmartIdContractSignature({
      application_id: application.id,
      contract_type: ContractType.SMALL_LOAN_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
  };

  const signAppWithIdCard = () => {
    const { id: application_id } = application;
    const lang = user?.language_abbr.split('-')[0];

    iSignApplet.init({
      certificatePurpose: 'sign',
      codebase: `${window.location.protocol}//${window.location.host}/dokobit`,
      language: lang,
      supportedResidencies: ['ee'],
    });

    setSignContractWithIDCardProcessing(true);

    (
      window as unknown as Window & {
        certificateSelected: (certificate: string) => void;
      }
    ).certificateSelected = (certificate: string) => {
      const cert = btoa(unescape(encodeURIComponent(certificate)));
      prepareIdCardContractSignature({
        application_id,
        certificate: cert,
        contract_type: ContractType.SMALL_LOAN_SIGNED,
      })
        .then(({ data: prepareSignatureData }) => {
          iSignApplet.setHashAlgorithm(
            prepareSignatureData?.challenge?.algorithm || '',
          );

          iSignApplet.sign(
            prepareSignatureData?.challenge?.dtbs || '',
            prepareSignatureData?.challenge?.dtbs_hash || '',
            (signedHash: string) => {
              signContractWithIdCard({
                token: prepareSignatureData?.challenge?.token || '',
                signature: signedHash,
              })
                .then(({ data }) => {
                  if (data?.success) {
                    executeSuccessfulSigningCallbacks();
                  } else {
                    toast.error(
                      te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError),
                    );
                  }
                })
                .catch(() => {
                  setSignContractWithIDCardProcessing(false);
                });
            },
          );
        })
        .catch(() => {
          setSignContractWithIDCardProcessing(false);
          toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        });
    };
  };

  const prepareSigningAppWithMobileId = () => {
    prepareMobileIdContractSignature({
      application_id: application.id,
      contract_type: ContractType.SMALL_LOAN_SIGNED,
    })
      .then(() => {
        setSigningPageViewType(SigningPageViewTypes.pinConfirmation);
      })
      .catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
  };

  const signAppWithMobileId = () =>
    signContractWithMobileId().then(({ data }) => {
      if (data?.success) {
        executeSuccessfulSigningCallbacks();
      } else {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      }
    });

  const signAppWithSmartId = () =>
    signContractWithSmartId().then(({ data }) => {
      if (data?.success) {
        executeSuccessfulSigningCallbacks();
      } else {
        toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
      }
    });

  const signAppByMobileIdOrSmartId = () => {
    if (signInMethod === UsersignInMethod.MOBILE) {
      return signAppWithMobileId();
    }
    // default is smart id
    return signAppWithSmartId();
  };

  const signAppWithBanklink = ({ payment_method_key }: FieldValues) => {
    prepareBanklinkContractSignature({
      application_id: application.id,
      contract_type: ContractType.SMALL_LOAN_SIGNED,
      payment_method_key,
      accept_url: banklinkSigningAcceptUrl,
      cancel_url: banklinkSigningCancelUrl,
    }).then(({ data }) => {
      const redirectUrl = data?.challenge?.redirect_url;
      const sessionId = data?.challenge?.session_id;

      const queryParamsToStore = getFilteredUrlSearchParamsObject(
        {
          [AppSearchParams.referenceKey]: true,
        },
        searchParams,
      );

      if (Object.keys(queryParamsToStore).length) {
        storeQueryParams(queryParamsToStore);
      }

      if (sessionId) {
        setToStorage(LocalStorageKeys.sessionId, sessionId);
      }

      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
    });
  };

  const onSigningFormSubmit = async (
    { iban, payment_method_key }: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => {
    if (!applicationUserInfoId) {
      throw new Error('Application user info id is required');
    }

    try {
      const shouldIbanBeUpdated =
        formMethods.formState.dirtyFields[FormFieldNames.iban] || !iban;

      if (shouldIbanBeUpdated) {
        const variablesFilteredByVisiblePageAttributes =
          filterObjectByExistingKeysInObject(
            {
              iban,
            },
            formMethods.control._fields,
          );

        await updateApplicationUserInfoSigning({
          application_user_info_id: applicationUserInfoId,
          ...variablesFilteredByVisiblePageAttributes,
        });
      }
      await signContract({
        payment_method_key,
      });
    } catch (error) {
      if ((error as Error)?.message === 'Unauthorized') {
        await getPageUrlAndNavigate(true);
      }
    }
  };

  const signContract = async (formFieldValues: FieldValues) => {
    if (onlyPasswordSigningEnabled) {
      signAppWithPassword();
      return;
    }

    switch (signInMethod) {
      case UsersignInMethod.PAYSERA_BANKLINK:
      case UsersignInMethod.MAGIC_LINK:
        signAppWithBanklink(formFieldValues);
        break;
      case UsersignInMethod.MOBILE:
        prepareSigningAppWithMobileId();
        break;
      case UsersignInMethod.SMART_ID:
        prepareSigningAppWithSmartId();
        break;
      case UsersignInMethod.ID_CARD:
        signAppWithIdCard();
        break;
      default:
        signAppWithPassword();
        break;
    }
  };

  const onTogglePaymentLeaveOffer = () => {
    updateApplicationCampaign({
      applicationId: application.id,
      referenceKey: applicationReferenceKey,
      paymentLeaveEnabled: !isPaymentLeaveEnabled,
    }).then(() => {
      setIsPaymentLeaveEnabled(!isPaymentLeaveEnabled);
    });
  };

  const onPaymentMethodTabChecked = (method: PayoutMethod) => {
    updateApplicationPaymentMethod({
      application_id: application.id,
      for_private_person: application.for_private_person,
      schedule_type: application.schedule_type,
      is_instant_payout: method === PayoutMethod.Instant,
    });
  };

  useEffect(() => {
    if (
      application.is_instant_payout_available &&
      pricingDataWithKeys[PRICING_KEYS.smallLoanInstantPayoutFee] === 0 &&
      !application.is_instant_payout
    ) {
      updateApplicationPaymentMethod({
        application_id: application.id,
        for_private_person: application.for_private_person,
        schedule_type: application.schedule_type,
        is_instant_payout: true,
      });
    }
  }, [application]);

  useEffectOnce(() => {
    if (storedQueryParams) {
      const params = Object.fromEntries(Array.from(searchParams));
      navigate(
        {
          search: createSearchParams({
            ...params,
            ...storedQueryParams,
          }).toString(),
        },
        { replace: true },
      );
      storeQueryParams(null);
    }
  });

  useEffectOnce(() => {
    getPageAttributes();
  });

  useEffect(() => {
    if (isBanklinkSigningPollSuccess) {
      stopBanklinkSigningStatusPolling();
      executeSuccessfulSigningCallbacks();
    }
  }, [isBanklinkSigningPollSuccess]);

  useEffectOnce(() => {
    getPricingData({
      keys: [PRICING_KEYS.smallLoanInstantPayoutFee],
    });
    getApplication();
  });

  useEffect(() => {
    if (
      (visiblePageAttributes[PageAttributeNames.banklink] ||
        signInMethod === UsersignInMethod.MAGIC_LINK) &&
      !banklinks.length
    ) {
      getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
    }
  }, [pageAttributes?.length]);

  useEffectOnce(() => {
    if (payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
      setSigningPageViewType(SigningPageViewTypes.pending);
      startBanklinkPaymentStatusPolling().catch(() => {
        setSigningPageViewType(SigningPageViewTypes.signing);
      });
    } else if (
      payseraSigningPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed
    ) {
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  });

  useEffect(() => {
    switch (banklinkPaymentStatus) {
      case BANKLINK_PAYMENT_POLL_STATUSES.success:
        stopBanklinkPaymentStatusPolling();
        startBanklinkSigningStatusPolling()
          .then(({ data }) => {
            if (data?.success) {
              stopBanklinkSigningStatusPolling();
              removeFromStorage(LocalStorageKeys.sessionId);
              executeSuccessfulSigningCallbacks();
            }
          })
          .catch((error) => {
            // Stop polling only if the code is not 3803
            // 3803 means that the Payment was not confirmed by Paysera
            if (error.code !== 3803) {
              stopBanklinkPaymentStatusPolling();
            }
          });
        break;
      case BANKLINK_PAYMENT_POLL_STATUSES.failed:
        stopBanklinkPaymentStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        setSigningPageViewType(SigningPageViewTypes.signing);
        break;
      default:
        break;
    }
  }, [banklinkPaymentStatus]);

  useEffect(() => {
    if (signInMethod === UsersignInMethod.ID_CARD) {
      loadAndInitializeIdCardScripts();
    }
  }, [signInMethod]);

  return useMemo(
    () => ({
      signingPageLoaded,
      processingSigningPage:
        pricingDataLoading ||
        pageUrlAndNavigationProcessing ||
        signContractWithPasswordProcessing ||
        smartIdContractSignaturePreparationLoading ||
        signContractWithIDCardProcessing ||
        mobileIdContractSignaturePreparationLoading ||
        applicationUserInfoSigningUpdating ||
        banklinkSigningProcessing,
      isApplicationPayoutMethodUpdating,
      visiblePageAttributes,
      onSigningFormSubmit,
      pricingDataWithKeys,
      contractLink,
      userCanSignContract,
      isInitialLoanAmountDisallowed,
      smartIdSigningChallengeViewIsVisible:
        Boolean(smartIdContractSignaturePreparationChallenge) &&
        !smartIdContractSignaturePreparationError,
      mobileIdSigningChallengeViewIsVisible:
        Boolean(mobileIdContractSignaturePreparationChallenge) &&
        !mobileIdContractSignaturePreparationError,
      smartIdContractSignaturePollChallengeId:
        smartIdContractSignaturePreparationChallenge?.challenge_id || '',
      mobileIdContractSignaturePollChallengeId:
        mobileIdContractSignaturePreparationChallenge?.challenge_id || '',
      banklinkOptions: banklinks,
      signingPageViewType,
      isBanklinkOptionSelected,
      selectBanklinkOption,
      onPinConfirmationCancel,
      signInMethod,
      onLogOutClick,
      application,
      onTogglePaymentLeaveOffer,
      isPaymentLeaveEnabled,
      applicationCampaignUpdating,
      signAppByMobileIdOrSmartId,
      onPaymentMethodTabChecked,
      signingPageFormConfig,
      userInfoValidationErrors,
    }),
    [
      signingPageLoaded,
      pricingDataLoading,
      pageUrlAndNavigationProcessing,
      signContractWithPasswordProcessing,
      smartIdContractSignaturePreparationLoading,
      signContractWithIDCardProcessing,
      mobileIdContractSignaturePreparationLoading,
      banklinkSigningProcessing,
      isApplicationPayoutMethodUpdating,
      visiblePageAttributes,
      onSigningFormSubmit,
      pricingDataWithKeys,
      contractLink,
      userCanSignContract,
      isInitialLoanAmountDisallowed,
      mobileIdContractSignaturePreparationChallenge,
      smartIdContractSignaturePreparationChallenge,
      mobileIdContractSignaturePreparationChallenge?.challenge_id,
      smartIdContractSignaturePreparationChallenge?.challenge_id,
      mobileIdContractSignaturePreparationError,
      smartIdContractSignaturePreparationError,
      signInMethod,
      banklinks,
      signingPageViewType,
      isBanklinkOptionSelected,
      selectBanklinkOption,
      onPinConfirmationCancel,
      onLogOutClick,
      application,
      onTogglePaymentLeaveOffer,
      isPaymentLeaveEnabled,
      applicationCampaignUpdating,
      signAppByMobileIdOrSmartId,
      onPaymentMethodTabChecked,
      signingPageFormConfig,
      userInfoValidationErrors,
      applicationUserInfoSigningUpdating,
    ],
  );
};
