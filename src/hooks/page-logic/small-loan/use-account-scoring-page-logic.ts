import { AccountStatementProviderType } from 'api/core/generated';
import {
  AccountScoringStatuses,
  AccountScoringViewTypes,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useAccountScoringSteps } from 'hooks/use-account-scoring-steps';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { usePollAccountScoringInvitation } from 'hooks/use-poll-account-scoring-invitation';
import { usePollAccountScoringStatement } from 'hooks/use-poll-account-scoring-statement';
import { useStoreAccountScoringInvitation } from 'hooks/use-store-account-scoring-invitation';
import { useUploadAccountStatement } from 'hooks/use-upload-account-statement';
import { useEffectOnce } from 'hooks/utils';
import type { AccountScoringStepsType } from 'models';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  formatApplicationToAccountScoringPageDataFormat,
  getDefaultAccountScoringViewType,
  removeFromStorage,
} from 'services';

type AccountScoringLogic = {
  accountScoringPageLoaded: boolean;
  accountScoringSteps: AccountScoringStepsType;
  finishAccountScoring: () => void;
  onAccessBankButtonClick: () => void;
  retriggerAccountScoringSteps: () => void;
  onAccountStatementUploaded: (statement: File) => void;
  accountScoringViewType: AccountScoringViewTypes;
  setAccountScoringViewType: (viewType: AccountScoringViewTypes) => void;
};

export const useAccountScoringPageLogic = (): AccountScoringLogic => {
  const { t } = useTranslation(LocizeNamespaces.errors);

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();

  const { getApplication, application } = useGetCurrentApplication();

  const { email, userName, language, productId, userId, productIdVariables } =
    formatApplicationToAccountScoringPageDataFormat(application);

  const [accountScoringViewType, setAccountScoringViewType] =
    useState<AccountScoringViewTypes>(getDefaultAccountScoringViewType());

  const { pathname, search } = useLocation();

  const { logAction } = useLogApplicationAction();
  const {
    accountScoringSteps,
    finishAccountScoringSteps,
    retriggerAccountScoringSteps,
    triggerAccountScoringSteps,
    resetAccountScoringSteps,
  } = useAccountScoringSteps(accountScoringViewType);
  const { uploadAccountStatement } = useUploadAccountStatement();
  const { accountScoringInvitationData, storeAccountScoringInvitation } =
    useStoreAccountScoringInvitation();
  const { accountScoringInvitation, startAccountScoringInvitationPolling } =
    usePollAccountScoringInvitation();
  const { accountScoringStatement, startAccountScoringStatementPolling } =
    usePollAccountScoringStatement();

  const finishAccountScoring = () => {
    // LOG ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringProcessFinished,
      });
    }

    finishAccountScoringSteps();
    removeFromStorage(LocalStorageKeys.accountScoringSteps);
    getPageUrlAndNavigate(true);
  };

  const handleAccountScoringStatuses = (status: string) => {
    switch (status) {
      case AccountScoringStatuses.outdated:
        setAccountScoringViewType(AccountScoringViewTypes.initial);
        resetAccountScoringSteps();
        toast.error(t(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
        break;
      case AccountScoringStatuses.completed:
        finishAccountScoring();
        break;
      default:
        break;
    }
  };

  const onAccountStatementUploaded = (statement: File) => {
    if (!userId) {
      throw new Error('User ID is missing');
    }

    uploadAccountStatement({
      user_id: userId,
      provider: AccountStatementProviderType.ACCOUNTSCORING,
      send_email: false,
      statement,
      ...productIdVariables,
    }).then(() => {
      setAccountScoringViewType(AccountScoringViewTypes.stepper);
      retriggerAccountScoringSteps();
    });
  };

  const onAccessBankButtonClick = () => {
    if (accountScoringInvitationData) {
      const { response_url } = accountScoringInvitationData;

      window.open(String(response_url), '_blank');

      // LOG ACTION
      if (productId) {
        logAction({
          productId,
          action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringRedirected,
        });
      }
    }

    setAccountScoringViewType(AccountScoringViewTypes.stepper);
    retriggerAccountScoringSteps();
  };

  useEffectOnce(() => {
    getApplication();
  });

  useEffectOnce(() => {
    if (accountScoringViewType === AccountScoringViewTypes.stepper) {
      triggerAccountScoringSteps();
    }
  });

  useEffect(() => {
    if (productId && userId) {
      // LOG ACTION
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.startingAccountScoring,
      });

      if (!email) {
        throw new Error('User email is missing');
      }
      if (!language) {
        throw new Error('Language is missing');
      }

      storeAccountScoringInvitation({
        user_id: userId,
        user_mail: email,
        user_name: userName,
        redirect_url: `${pathname}${search}`,
        language,
        ...productIdVariables,
      });

      startAccountScoringStatementPolling({
        user_id: userId,
        ...productIdVariables,
      });
    }
  }, [productId, userId]);

  useEffect(() => {
    if (accountScoringInvitationData) {
      if (!userId) {
        throw new Error('User ID is missing');
      }
      startAccountScoringInvitationPolling({
        response_id: Number(accountScoringInvitationData.response_id),
        user_id: userId,
        ...productIdVariables,
      });
    }
  }, [accountScoringInvitationData]);

  useEffect(() => {
    if (accountScoringInvitation) {
      handleAccountScoringStatuses(accountScoringInvitation.process_status);
    }
  }, [accountScoringInvitation]);

  useEffect(() => {
    if (accountScoringStatement) {
      handleAccountScoringStatuses(accountScoringStatement.process_status);
    }
  }, [accountScoringStatement]);

  return useMemo(
    () => ({
      accountScoringSteps,
      finishAccountScoring,
      retriggerAccountScoringSteps,
      onAccountStatementUploaded,
      accountScoringViewType,
      setAccountScoringViewType,
      onAccessBankButtonClick,
      accountScoringPageLoaded:
        !pageUrlAndNavigationProcessing &&
        Boolean(accountScoringInvitationData),
    }),
    [
      accountScoringSteps,
      finishAccountScoring,
      retriggerAccountScoringSteps,
      onAccountStatementUploaded,
      accountScoringViewType,
      onAccessBankButtonClick,
      pageUrlAndNavigationProcessing,
      accountScoringInvitationData,
    ],
  );
};
