import {
  AppRoutePaths,
  REDIRECT_URLS,
  SmallLoanRoutePaths,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspense';
import { useRudderStack } from 'hooks/use-rudderstack';
import type { PendingPageLogic } from 'models';
import { useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { convertPageAttributeNamesToObject } from 'services';

export const usePendingPageLogic = (): PendingPageLogic => {
  const { ruderStackEvents } = useRudderStack();
  const { search } = useLocation();
  const {
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    applicationPrivateInfo,
    user,
  } = useRootContext();

  const { pageAttributes } = useGetPageAttributesSuspense();

  const [isRedirecting, setIsRedirecting] = useState(false);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const unpaidInvoiceAmount = user?.unpaid_invoice_amount;
  const referenceKey = user?.reference_key;

  const creditAccount = user?.credit_accounts?.[0];
  const creditLimit = creditAccount?.credit_limit ?? 0;
  const unpaidPrincipal = creditAccount?.unpaid_principal ?? 0;
  const availableCreditLimit = creditLimit - unpaidPrincipal;

  const onEstoAccountButtonClick = () => {
    window.location.href = REDIRECT_URLS.newCustomerProfile;
  };

  const sendAnalyticsEvent = () => {
    if (applicationPrivateInfo) {
      ruderStackEvents.verifyInformation({
        applicationId: applicationPrivateInfo.application_id,
      });
    }
  };

  const onCheckIncomeButtonClick = async () => {
    sendAnalyticsEvent();
    getPageUrlAndNavigate(null, {
      customCurrentPageUrl: `/${AppRoutePaths.SMALL_LOAN}/${SmallLoanRoutePaths.CONTACT_EXTRA}${search}`,
    });
  };

  const onPayDebtButtonClick = () => {
    if (!referenceKey) {
      throw new Error('Reference key is missing');
    }
    const paymentUrl = `${REDIRECT_URLS.newCustomerProfileInvoicePayment}?referenceKey=${referenceKey}`;
    window.location.href = paymentUrl;
  };

  const onCreditLineOnboardingButtonClick = () => {
    setIsRedirecting(true);
    window.location.href = REDIRECT_URLS.newCustomerProfileCreditLine;
  };

  const onCreditLineWithdrawalOnboardingButtonClick = () => {
    setIsRedirecting(true);
    window.location.href = REDIRECT_URLS.creditAccountWithdrawal;
  };

  return {
    visiblePageAttributes,
    debtAmount: unpaidInvoiceAmount,
    onPayDebtButtonClick,
    onEstoAccountButtonClick,
    onCheckIncomeButtonClick,
    pageUrlAndNavigationProcessing,
    isRedirecting,
    onCreditLineOnboardingButtonClick,
    onCreditLineWithdrawalOnboardingButtonClick,
    availableCreditLimit,
    creditLimit,
  };
};
