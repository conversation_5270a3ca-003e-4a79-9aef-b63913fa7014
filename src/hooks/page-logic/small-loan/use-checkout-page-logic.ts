import type { CreditSettingCalculator } from 'api/core/generated';
import {
  AppProductType,
  DEFAULT_DEBOUNCE_TIME,
  FormFieldNames,
  GoogleAnalyticsEvents,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetApplicationByReference } from 'hooks/use-get-application-by-reference';
import { useGetCreditSettings } from 'hooks/use-get-credit-settings';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetPageAttributes } from 'hooks/use-get-page-attributes';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useUpdateApplicationCampaign } from 'hooks/use-update-application-campaign-mutation';
import { useUpdateApplicationCreditInfo } from 'hooks/use-update-application-credit-info';
import { useDebounce, useEffectOnce } from 'hooks/utils';
import { useEffect, useMemo, useState } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import {
  convertPageAttributeNamesToObject,
  getApplicationDefaultDownPayment,
  getCheckoutPageRangeValues,
  getDefaultPeriodMonths,
  getUpdateApplicationCreditInfoVariables,
  roundNumberToNearestTen,
  roundNumberToUpperTen,
  setSmallLoanAmountToStorage,
} from 'services';

export const useCheckoutPageLogic = () => {
  const { t: te } = useTranslation(LocizeNamespaces.errors);

  const { logAction } = useLogApplicationAction();
  const {
    user,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    applicationPrivateInfo,
    trackGoogleAnalyticsEvent,
  } = useRootContext();
  const { pageAttributes, pageAttributesLoading, getPageAttributes } =
    useGetPageAttributes();
  const {
    getApplication,
    application,
    applicationLoading,
    applicationReferenceKey,
  } = (user ? useGetCurrentApplication : useGetApplicationByReference)();
  const {
    getCreditSettings,
    creditSettings,
    creditSettingsLoading,
    refetchCreditSettings,
  } = useGetCreditSettings();
  const { updateApplicationCreditInfo, applicationCreditInfoUpdating } =
    useUpdateApplicationCreditInfo();
  const { updateApplicationCampaign, applicationCampaignUpdating } =
    useUpdateApplicationCampaign();

  const [selectedPeriod, setSelectedPeriod] = useState<number>(0);
  const [isPaymentLeaveEnabled, setIsPaymentLeaveEnabled] = useState(false);

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const onLoanAmountChange = (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => {
    const { net_total: netTotal, period_months: periodMonths } =
      formFieldValues;
    const { clearErrors, setError, setValue } = formMethods;
    const { min_amount, max_amount } = applicationPrivateInfo ?? {};

    const loanAmountIsValid = netTotal >= min_amount && netTotal <= max_amount;

    if (loanAmountIsValid) {
      const { id: application_id } = application;

      clearErrors(FormFieldNames.netTotal);
      setValue(FormFieldNames.netTotal, roundNumberToNearestTen(netTotal));

      refetchCreditSettings({ net_total: +netTotal, application_id }).then(
        ({ data }) => {
          const settings =
            data?.settings as Array<CreditSettingCalculator> | null;

          const hasSelectedPeriodInSettings = settings?.find(
            ({ month }) => month === selectedPeriod,
          );

          const maxPeriod = Number(settings?.[settings.length - 1].month);

          if (!hasSelectedPeriodInSettings) {
            setSelectedPeriod(maxPeriod);
            setValue(FormFieldNames.periodMonths, maxPeriod);
          }

          updateApplicationCreditInfo(
            getUpdateApplicationCreditInfoVariables({
              application,
              applicationReferenceKey,
              periodMonths: hasSelectedPeriodInSettings
                ? periodMonths
                : maxPeriod,
              netTotal,
              downPayment: application.credit_info?.down_payment ?? 0,
            }),
          );
        },
      );
    } else {
      setError(FormFieldNames.netTotal, {});
    }
  };

  const onPeriodChange = (formFieldValues: FieldValues) => {
    setSelectedPeriod(+formFieldValues.period_months);
  };

  const debouncedPeriodChange = useDebounce(
    onPeriodChange,
    DEFAULT_DEBOUNCE_TIME,
  );

  const onCheckoutPageFormSubmit = async (formFieldValues: FieldValues) => {
    const {
      period_months: periodMonths,
      down_payment: downPayment,
      net_total: netTotal,
    } = formFieldValues;

    try {
      await updateApplicationCreditInfo(
        getUpdateApplicationCreditInfoVariables({
          application,
          applicationReferenceKey,
          downPayment,
          periodMonths,
          netTotal,
        }),
      );

      setSmallLoanAmountToStorage(+netTotal, application.id);

      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.checkoutCompleted);
      getPageUrlAndNavigate(true);
    } catch (error) {
      console.error(error);
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  };

  const onTogglePaymentLeaveOffer = async () => {
    try {
      await updateApplicationCampaign({
        applicationId: application.id,
        referenceKey: applicationReferenceKey,
        paymentLeaveEnabled: !isPaymentLeaveEnabled,
      });
      setIsPaymentLeaveEnabled(!isPaymentLeaveEnabled);
    } catch (error) {
      console.error(error);
      toast.error(te(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  };

  const rangeValues = getCheckoutPageRangeValues(
    AppProductType.SMALL_LOAN,
    applicationPrivateInfo || {},
    [],
  );

  const periodValues = creditSettings?.map(({ month }) => month) || [];

  const checkoutPageLoaded = (() =>
    Boolean(creditSettings) &&
    !pageUrlAndNavigationProcessing &&
    !pageAttributesLoading &&
    ((Boolean(application.id) && applicationLoading) || !applicationLoading))();

  const checkoutPageFormConfig = {
    defaultValues: {
      [FormFieldNames.periodMonths]: getDefaultPeriodMonths(
        application.credit_info?.period_months ?? 0,
        creditSettings,
      ),
      [FormFieldNames.downPayment]:
        getApplicationDefaultDownPayment(application),
      [FormFieldNames.netTotal]: roundNumberToUpperTen(
        application.requested_amount,
      ),
    },
  };

  const nextButtonIsDisabled =
    pageUrlAndNavigationProcessing ||
    creditSettingsLoading ||
    applicationCampaignUpdating ||
    applicationLoading ||
    applicationCreditInfoUpdating;

  useEffectOnce(() => {
    getPageAttributes();
  });

  useEffect(() => {
    if (application.id) {
      const paymentLeaveEnabled = Boolean(
        application.campaign?.payment_leave_enabled,
      );

      setIsPaymentLeaveEnabled(paymentLeaveEnabled);
    }
  }, [application]);

  useEffectOnce(() => {
    getApplication();
  });

  useEffect(() => {
    const { id: application_id, credit_info } = application;

    if (credit_info && application_id) {
      const { net_total, down_payment } = credit_info;
      getCreditSettings({
        net_total,
        down_payment,
        application_id,
        consider_max_monthly_payment: false,
      });
    }
  }, [application]);

  useEffect(() => {
    if (!selectedPeriod && application?.credit_info && creditSettings) {
      setSelectedPeriod(
        getDefaultPeriodMonths(
          application?.credit_info?.period_months,
          creditSettings,
        ),
      );
    }
  }, [application, creditSettings]);

  useEffect(() => {
    if (application.id) {
      logAction({
        productId: application.id,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingCreditSettings,
      });
    }
  }, [application.id]);

  return useMemo(
    () => ({
      application,
      debouncedPeriodChange,
      onLoanAmountChange,
      periodValues,
      rangeValues,
      checkoutPageLoaded,
      processingCheckout: pageUrlAndNavigationProcessing,
      checkoutPageFormConfig,
      onCheckoutPageFormSubmit,
      visiblePageAttributes,
      nextButtonIsDisabled,
      onTogglePaymentLeaveOffer,
      isPaymentLeaveEnabled,
    }),
    [
      application,
      debouncedPeriodChange,
      onLoanAmountChange,
      periodValues,
      rangeValues,
      checkoutPageLoaded,
      pageUrlAndNavigationProcessing,
      checkoutPageFormConfig,
      onCheckoutPageFormSubmit,
      visiblePageAttributes,
      nextButtonIsDisabled,
      onTogglePaymentLeaveOffer,
      isPaymentLeaveEnabled,
    ],
  );
};
