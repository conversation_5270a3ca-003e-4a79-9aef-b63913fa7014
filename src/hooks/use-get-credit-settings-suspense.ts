import {
  NetworkStatus,
  type SuspenseQueryHookOptions,
  useApolloClient,
} from '@apollo/client';
import {
  type CreditSettingCalculator,
  CreditSettingsDocument,
  type CreditSettingsQuery,
  type CreditSettingsQueryVariables,
  useCreditSettingsSuspenseQuery,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';
import { useState } from 'react';

export const useGetCreditSettingsSuspense = (
  variables: CreditSettingsQueryVariables,
  options?: SuspenseQueryHookOptions<
    CreditSettingsQuery,
    CreditSettingsQueryVariables
  >,
) => {
  const client = useApolloClient();
  const [quietRefetchLoading, setQuietRefetchLoading] = useState(false);

  const { data, error, networkStatus, refetch } =
    useCreditSettingsSuspenseQuery({
      variables,
      context: { apiVersion: AppApiVersions.core },
      ...options,
    });

  const quietCreditSettingsRefetch = async (
    refetchVariables: CreditSettingsQueryVariables,
  ) => {
    setQuietRefetchLoading(true);
    try {
      const result = await client.query<CreditSettingsQuery>({
        query: CreditSettingsDocument,
        variables: refetchVariables,
        context: { apiVersion: AppApiVersions.core },
      });
      return result.data.settings;
    } finally {
      setQuietRefetchLoading(false);
    }
  };

  const refetchCreditSettings = (
    refetchVariables?: CreditSettingsQueryVariables,
  ) => refetch(refetchVariables);

  return {
    creditSettings: (data?.settings ?? []) as Array<CreditSettingCalculator>,
    creditSettingsLoading: networkStatus === NetworkStatus.loading,
    creditSettingsError: error,
    creditSettingsWasFetched: networkStatus === NetworkStatus.ready,
    quietCreditSettingsRefetch,
    quietCreditSettingsRefetchLoading: quietRefetchLoading,
    refetchCreditSettings,
  };
};
