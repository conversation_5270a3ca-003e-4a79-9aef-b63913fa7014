export enum AppFormFieldTypes {
  text = 'text',
  phone = 'tel',
  number = 'number',
  email = 'email',
  checkbox = 'checkbox',
  password = 'password',
  period = 'period',
  select = 'select',
  banklink = 'banklink',
  downPayment = 'downPayment',
  range = 'range',
  date = 'date',
}

export enum FormFieldNames {
  purposeOfLoan = 'purpose_of_loan',
  email = 'email',
  phone = 'phone',
  address = 'address',
  name = 'name',
  city = 'city',
  postCode = 'post_code',
  politicalExposure = 'political_exposure',
  conditionsAgreement = 'conditions_agreement',
  newsletterAgreement = 'newsletter_agreement',
  username = 'username',
  pin = 'pin',
  password = 'password',
  smartId = 'smartId',
  iban = 'iban',
  paymentMethodKey = 'payment_method_key',
  periodMonths = 'period_months',
  downPayment = 'down_payment',
  netIncomeMonthly = 'net_income_monthly',
  monthlyLivingExpenses = 'monthly_living_expenses',
  totalExpenses = 'total_expenses',
  instructionsSendingMethod = 'instructions_sending_method',
  expenditureMonthly = 'expenditure_monthly',
  numberOfDependents = 'number_of_dependents',
  netTotal = 'net_total',
  employmentDate = 'employment_date',
  spouseEmploymentDate = 'spouse_employment_date',
  planningNewDebts = 'planning_new_debts',
  futureReducedEarnings = 'future_reduced_earnings',
  ultimateBeneficialOwner = 'ultimate_beneficial_owner',
  spousePhone = 'spouse_phone',
  spouseEmail = 'spouse_email',
  spouseApplicationMethod = 'spouse_application_method',
  spousePin = 'spouse_pin',
  spouseSigningMethod = 'spouse_signing_method',
  spouseNetIncomeMonthly = 'spouse_net_income_monthly',
  spouseMonthlyLivingExpenses = 'spouse_monthly_living_expenses',
  spouseTotalExpenses = 'spouse_total_expenses',
  spouseExpenditureMonthly = 'spouse_expenditure_monthly',
  occupationCategory = 'occupation_category',
  addLegalPersonToInvoice = 'add_legal_person_to_invoice',
  legalPerson = 'legal_person',
  overdueDebt = 'overdue_debt',
  spouseOverdueDebt = 'spouse_overdue_debt',
  conditionsAndPensionAgreement = 'conditions_and_pension_agreement',
  creditLimit = 'credit_limit',
  insuranceOption = 'insurance_option',
  insuranceTermsOfService = 'insurance_terms_of_service',
  insuranceDataPolicy = 'insurance_data_policy',
  insuranceHealthAndEmployment = 'insurance_health_and_employment',
}

export enum FormFieldVariants {
  primary = 'primary',
  secondary = 'secondary',
  login = 'login',
}

export enum FormValidationErrorTypes {
  invalidDownPaymentAmount = 'invalid-down-payment',
}

export const DEFAULT_NUMBER_OF_DEPENDENTS = '0';

export enum FormTypes {
  primary = 'primary',
  secondary = 'secondary',
}
