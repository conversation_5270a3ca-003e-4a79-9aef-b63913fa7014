import { REDIRECT_URLS } from 'app-constants';
import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants/locize.constants';
import { AppExternalLink } from 'components';
import { AppLocalizationComponent } from 'components/app-localization-component';
import { Typography } from 'components/typography/Typography';
import { CONTACT_INFO } from 'environment';
import type { AnyObject } from 'models';
import { useTranslation } from 'react-i18next';

export const MainFooter = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.common);
  const { contactName } = CONTACT_INFO;
  return (
    <div className="text-start">
      <Typography variant="text-s" className="text-neutral-400" tag="div">
        <AppLocalizationComponent
          components={{
            site_link: (
              <AppExternalLink
                className="cursor-pointer hover:text-primary-brand02"
                to={(REDIRECT_URLS.termsPageUrs as AnyObject)[i18n.language]}
              />
            ),
            home_link: (
              <AppExternalLink
                className="cursor-pointer hover:text-primary-brand02"
                to={REDIRECT_URLS.homepageRegionUrl}
              />
            ),
          }}
          className="text-[.750rem]"
          locizeKey={LOCIZE_COMMON_TRANSLATION_KEYS.footerText1}
          t={t}
          values={{
            contact_name: contactName,
            homepage: REDIRECT_URLS.homepageRegionUrl.replace(
              /^https?:\/\//,
              '',
            ),
          }}
        />
      </Typography>
    </div>
  );
};
