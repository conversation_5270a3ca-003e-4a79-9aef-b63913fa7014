import {
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { LanguageSelector } from 'components/language-selector';
import { LogoutButton } from 'components/logout-button';
import { useRootContext } from 'context/root';
import { isDekker, isDevelopment } from 'environment';
import Logo from 'icons/logo.svg?react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

type MainHeaderProps = {
  className?: string;
};

export const MainHeader = ({ className }: MainHeaderProps) => {
  const { user } = useRootContext();
  const { t } = useTranslation(LocizeNamespaces.common);

  return (
    <div
      className={cn(
        'z-10 flex items-center justify-between gap-4 border-neutral-200 border-b bg-primary-white',
        className,
      )}
    >
      <Logo className="shrink-0" />
      <div className="flex items-center gap-2">
        {user?.id && (isDekker || isDevelopment) ? (
          <LogoutButton
            variant="grey"
            className="h-4 p-0 mt-0 mr-2 text-primary-black bg-transparent hover:bg-transparent hover:text-primary-brand02"
          >
            {t(LOCIZE_COMMON_TRANSLATION_KEYS.logoutButtonLabel)}
          </LogoutButton>
        ) : null}
        <LanguageSelector />
      </div>
    </div>
  );
};
