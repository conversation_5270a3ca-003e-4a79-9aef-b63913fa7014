import { useIsMobileView } from 'hooks/system';
import type { ReactNode } from 'react';
import { cn } from 'utils/tailwind';
import { MainFooter } from 'widgets/footers/main-footer';
import { MainHeader } from 'widgets/headers/main-header';

import { ContainerLayout } from '../container-layout/ContainerLayout';
import { DualPanelLayout } from '../dual-panel-layout/DualPanelLayout';

type PageLayoutProps = {
  left: ReactNode;
  right: ReactNode;
  rightHeader?: ReactNode;
  className?: string;
};

export const PageLayout = ({
  left,
  right,
  rightHeader,
  className,
}: PageLayoutProps) => {
  const isMobileView = useIsMobileView();
  return (
    <div
      className={cn(
        'relative flex flex-col min-h-screen md:h-full md:block',
        className,
      )}
    >
      {isMobileView ? (
        <ContainerLayout
          className={cn(
            'sticky top-0 z-10 bg-primary-white md:relative md:bg-transparent',
            isMobileView && 'border-b border-solid border-neutral-200',
          )}
        >
          <MainHeader />
        </ContainerLayout>
      ) : null}
      <DualPanelLayout
        rightPanelId="rightDualPanel"
        left={
          <>
            {isMobileView ? null : (
              <ContainerLayout className="sticky top-0 z-10 bg-primary-white md:relative md:bg-transparent">
                <MainHeader className="bg-transparent" />
              </ContainerLayout>
            )}

            <ContainerLayout
              id="leftBody"
              className="grid grid-cols-1 mt-auto overflow-y-auto place-items-center no-scrollbar"
              noYPadding
            >
              <div className="flex flex-col w-full items-center justify-center max-w-[25rem]">
                {left && left}
              </div>
            </ContainerLayout>

            {isMobileView ? null : (
              <ContainerLayout className="mt-auto bg-neutral-50 md:bg-transparent">
                <MainFooter />
              </ContainerLayout>
            )}
          </>
        }
        right={
          <>
            {rightHeader && rightHeader}
            <ContainerLayout
              id="rightBody"
              className="pt-8 grid grid-cols-1 justify-center items-center pb-20 overflow-y-auto no-scrollbar md:py-0 md:h-full"
            >
              <div className="h-full flex flex-col w-full max-w-[25rem] mx-auto place-items-center justify-center content-center md:[&>*:first-child]:pb-[5.125rem] md:[&>*:first-child]:pt-[5.5rem]">
                {right && right}
              </div>
            </ContainerLayout>
          </>
        }
      />
      {isMobileView ? (
        <ContainerLayout className="mt-auto bg-neutral-50 md:bg-transparent">
          <MainFooter />
        </ContainerLayout>
      ) : null}
    </div>
  );
};
