import type {
  <PERSON><PERSON><PERSON>yR<PERSON>ult,
  FetchR<PERSON>ult,
  MutationFunctionOptions,
} from '@apollo/client';
import type {
  Application,
  ApplicationScheduleType,
  CreditSettingCalculator,
  InsuranceFragment,
  InsuranceProviderOption,
  KlixPaymentStatusType,
  MeCreditAccountFragment,
  MeQuery,
  MeUserFragment,
  StoreInsuranceMutation,
  StoreInsuranceMutationVariables,
  StripePaymentStatusType,
  UserCreditAccountFragment,
  UsersignInMethod,
} from 'api/core/generated';
import type {
  AppLoginMethods,
  AppProductType,
  FormFieldNames,
  FormTypes,
  GoogleAnalyticsEvents,
  SigningPageViewTypes,
} from 'app-constants';
import type {
  SigningFormType,
  SpouseConsentPageFormType,
} from 'hooks/page-logic/credit-line';
import type { SigningPageFormType } from 'hooks/page-logic/income-insurance/use-signing-page-logic';
import type { SigningFormType as SmallLoanSigningFormType } from 'hooks/page-logic/small-loan';
import type { Dispatch, SetStateAction } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';

import type { AppButtonProps } from './button.models';
import type { AnyObject, Option } from './core.models';
import type { IncomeSourcesCheckServiceOption } from './emta-consent.models';
import type { BanklinkOptions, FormConfig } from './form.models';
import type { GoogleAnalyticsEventData } from './google-analytics.models';
import type { SummaryTableObject } from './object.models';
import type { VisiblePageAttributes } from './page-attributes.models';
import type { FieldValidationObject } from './validation.models';

export type RootLogic = {
  pageUrl: string | undefined;
  productType: AppProductType | null;
  application: Application;
  languages: Array<AnyObject>;
  applicationPrivateInfo: AnyObject | null;
  applicationPrivateInfoLoading: boolean;
  currentStepCount: number | null;
  isTablet: boolean;
  isMobile: boolean;
  user: Nullable<MeUserFragment>;
  userLoading: boolean;
  userWasFetched: boolean;
  getUser: () => Promise<ApolloQueryResult<MeQuery>>;
  quietUserRefetch: () => Promise<MeUserFragment | null | undefined>;
  getPageUrlAndNavigate: (
    next?: boolean | null,
    options?: AnyObject,
  ) => Promise<unknown>;
  pageUrlAndNavigationProcessing: boolean;
  getApplicationPrivateInfo: () => void;
  trackGoogleAnalyticsEvent: (
    event: GoogleAnalyticsEvents,
    data?: GoogleAnalyticsEventData,
  ) => void;
};

export type IncomeInsuranceRootLogic = {
  storeInsurance: (
    options?: MutationFunctionOptions<
      StoreInsuranceMutation,
      StoreInsuranceMutationVariables
    >,
  ) => Promise<FetchResult<StoreInsuranceMutation>>;
  insuranceProviderOptions: Nullish<Array<Nullable<InsuranceProviderOption>>>;
  storedInsurance: Nullish<InsuranceFragment>;
  storeAndRedirectWithUpdatedInsuranceHash: () => Promise<void>;
  isStoringInsurance: boolean;
  resetInsuranceInstance: () => void;
};

export type CheckoutPageLogic = {
  pricingDataWithKeys: AnyObject;
  application: AnyObject;
  getMinMaxDownPayment: (period: string) => AnyObject;
  periodValues: Array<number>;
  checkoutPageLoaded: boolean;
  processingCheckout: boolean;
  nextButtonIsDisabled: boolean;
  checkoutPageFormConfig: FormConfig;
  debouncedDownPaymentChange: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  debouncedPeriodChange: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  onLoanAmountChange: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  onCreditAmountChange: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  onCheckoutPageFormSubmit: (formFieldValues: FieldValues) => void;
  visiblePageAttributes: VisiblePageAttributes;
  shouldShowDownPaymentField: boolean;
  rangeValues: Array<string> | Array<number>;
  isHirePurchase: boolean;
  isSmallLoan: boolean;
  isCreditLine: boolean;
  isCreditLineInfoOpen: boolean;
  setIsCreditLineInfoOpen: Dispatch<SetStateAction<boolean>>;
  initialCreditAmount: number;
  onTogglePaymentLeaveOffer: () => void;
  isPaymentLeaveEnabled: boolean;
  hirePurchaseSummary: Array<SummaryTableObject>;
};

export type HirePurchaseSigningPageLogic = {
  signInMethod: Nullable<UsersignInMethod>;
  signingPageLoaded: boolean;
  processingSigningPage: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  getMinMaxDownPayment: (period: string) => AnyObject;
  onSigningFormSubmit: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  debouncedDownPaymentChange: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  onPeriodChange: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  signingPageFormConfig: FormConfig;
  contractLink: string;
  userCanSignContract: boolean;
  downPaymentFieldValue: number;
  applicationCreditInfoUpdating: boolean;
  smartIdSigningChallengeViewIsVisible: boolean;
  mobileIdSigningChallengeViewIsVisible: boolean;
  smartIdContractSignaturePollChallengeId: string;
  mobileIdContractSignaturePollChallengeId: string;

  banklinkOptions: BanklinkOptions;
  signingPageViewType: SigningPageViewTypes;
  shouldShowDownPaymentField: boolean;
  isBanklinkOptionSelected: boolean;
  selectBanklinkOption: () => void;
  onPinConfirmationCancel: () => void;
  onLogOutClick: () => void;
  application: NonNullable<Application>;
  onTogglePaymentLeaveOffer: () => void;
  isPaymentLeaveEnabled: boolean;
  applicationCampaignUpdating: boolean;
  firstDueAtDate: string;
  firstDueAtDateLoading: boolean;
  signAppByMobileIdOrSmartId: () => Promise<void>;
  periodValues: Array<number>;
  selectedCreditSetting: Nullable<CreditSettingCalculator>;
  updatingApplicationConditions: boolean;
};

export type SmallLoanSigningPageLogic = {
  signInMethod: Nullable<UsersignInMethod>;
  processingSigningPage: boolean;
  smartIdContractSignaturePollChallengeId: string;
  mobileIdContractSignaturePollChallengeId: string;
  banklinkOptions: BanklinkOptions;
  signingPageViewType: SigningPageViewTypes;
  onPinConfirmationCancel: () => void;
  onLogOutClick: () => void;
  application: NonNullable<Application>;
  onTogglePaymentLeaveOffer: () => void;
  isPaymentLeaveEnabled: boolean;
  applicationCampaignUpdating: boolean;
  signAppByMobileIdOrSmartId: () => Promise<void>;
  onPaymentMethodTabChecked: (checked: boolean) => void;
  onFormInstantPayoutChange: (checked: boolean) => void;
  isApplicationPayoutMethodUpdating: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  signAppWithBanklink?: (payment_method_key: string) => void;
  pricingData: AnyObject;
  contractLink: string;
  userCanSignContract: boolean;
  isInitialLoanAmountDisallowed: boolean;
  onSigningFormSubmit: (formFieldValues: FieldValues) => void;
  signingPageFormConfig: UseFormReturn<SmallLoanSigningFormType>;
  userInfoValidationErrors: FieldValidationObject;
  form: UseFormReturn<SmallLoanSigningFormType>;
  isPayseraSigningMethodDialogOpen: boolean;
  setIsPayseraSigningMethodDialogOpen: Dispatch<SetStateAction<boolean>>;
};

export type CreditLineSigningPageLogic = {
  signInMethod: Nullable<UsersignInMethod>;
  signingPageLoaded?: boolean;
  processingSigningPage: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  userCreditAccount: Nullable<UserCreditAccountFragment>;
  onSigningFormSubmit: (formFieldValues: FieldValues) => void;
  onSigningButtonClick?: ({
    onBanklinkSigning,
  }: {
    onBanklinkSigning: () => void;
  }) => void;
  signAppWithBanklink?: (payment_method_key: string) => void;
  pricingDataWithKeys: AnyObject;
  contractLink: string;
  userCanSignContract: boolean;
  banklinkOptions: BanklinkOptions;
  smartIdContractSignaturePollChallengeId: string;
  mobileIdContractSignaturePollChallengeId: string;
  signingPageViewType: SigningPageViewTypes;
  isBanklinkOptionSelected?: boolean;
  onPinConfirmationCancel: () => void;
  onCreditAmountChange?: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  rangeValues?: string[] | number[];
  maxCreditAmount: number;
  isAvailableCreditAmount: boolean;
  creditAccountUpdating: boolean;
  onLogOutClick?: () => void;
  signAppByMobileIdOrSmartId: () => Promise<void>;
  form: UseFormReturn<SigningFormType>;
  userInfoValidationErrors: FieldValidationObject;
  isPayseraSigningMethodDialogOpen: boolean;
  setIsPayseraSigningMethodDialogOpen: Dispatch<SetStateAction<boolean>>;
};

export type IncomeInsuranceSigningPageLogic = {
  processingSigning: boolean;
  smartIdSigningChallengeViewIsVisible: boolean;
  mobileIdSigningChallengeViewIsVisible: boolean;
  smartIdContractSignaturePollChallengeId: string;
  mobileIdContractSignaturePollChallengeId: string;
  form: UseFormReturn<SigningPageFormType>;
  signingPageViewType: SigningPageViewTypes;
  onPinConfirmationCancel: () => void;
  onFormSubmit: ({
    onBanklinkSigning,
  }: {
    onBanklinkSigning: () => void;
  }) => (data: SigningPageFormType) => Promise<void>;
  insuranceProviderOptions: Nullish<Array<Nullable<InsuranceProviderOption>>>;
  shouldShowEmailPhoneFields: boolean;
  userCanSignContract: boolean;
  signAppWithBanklink: (payment_method_key: string) => void;

  banklinkOptions: BanklinkOptions;
  signInMethod: Nullable<UsersignInMethod>;
  signAppByMobileIdOrSmartId: () => Promise<void>;
  userUpdateErrors: FieldValidationObject;
};

export type ContactPageLogic = {
  contactPageLoaded: boolean;
  processingContactPage: boolean;
  contactPageFormConfig: FormConfig;
  onContactFormSubmit: (formFieldValues: FieldValues) => void;
  politicalExposureOptions: Array<Option>;
  politicalExposuresLoading: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  userInfoValidationErrors: FieldValidationObject;
  phonePrefix: string;
};

export type ContactExtraPageLogic = {
  onAddLegalPersonToInvoiceChange: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  onContactExtraFormSubmit: (
    formFieldValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  contactExtraPageFormConfig: FormConfig;
  contactExtraPageLoaded: boolean;
  processingContactExtraPage: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  onChangeTotalExpenses: (
    formValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  userInfoExtraValidationErrors: FieldValidationObject;
  sendingConsentLinkValidationErrors: FieldValidationObject;
  setFormType: (formType: FormTypes) => void;
  formType: FormTypes;
  phonePrefix: string;
  sendingConsentLink: boolean;
  instructionsSent: boolean;
  setInstructionsSent: (instructionSent: boolean) => void;
  occupationCategoryOptions: Array<Option>;
  legalPeopleOptions: Array<Option>;
  employmentDateOptions: Array<Option>;
  addLegalPersonToInvoiceDisabled: boolean;
  legalPeopleLoading: boolean;
};

export type SpouseConsentPageLogic = {
  onPinConfirmationCancel: () => void;
  disablingEmptyFieldNames: Array<FormFieldNames>;
  processingSpouseSigning: boolean;
  onSpouseConsentFormSubmit: (formFieldValues: FieldValues) => void;
  spouseConsentPageFormConfig: FormConfig;
  spouseConsentPageLoaded: boolean;
  processingSpouseConsentPage: boolean;
  spouseConsentFormValidationErrors: FieldValidationObject;
  onChangeTotalSpouseExpenses: (
    formValues: FieldValues,
    formMethods: UseFormReturn<FieldValues>,
  ) => void;
  spouseName: string;
  spouseConsentEmploymentDateOptions: Array<Option>;
  spouseConsentSigningMethodSelectOptions: Array<Option>;
  selectedSpouseConsentSigningMethod: Option | null;
  setSelectedSpouseConsentSigningMethod: Dispatch<
    SetStateAction<Option | null>
  >;

  banklinkOptions: BanklinkOptions;
  isBanklinkSigningAllowed: boolean;
  spouseConsentPageViewType: string;
  smartIdSpouseSignaturePollChallengeId: string;
  mobileIdSpouseSignaturePollChallengeId: string;
  isMobileIdSigningMethod: boolean;
  isBanklinkSigningMethod: boolean;
  signAppByMobileIdOrSmartId: () => Promise<void>;
};

export type CreditLineSpouseConsentPageLogic = {
  onPinConfirmationCancel: () => void;
  form: UseFormReturn<SpouseConsentPageFormType>;
  onFormSubmit: (formFieldValues: SpouseConsentPageFormType) => Promise<void>;
  spouseConsentFormValidationErrors: FieldValidationObject;
  userName: string;
  spouseConsentEmploymentDateOptions: Array<Option>;
  spouseConsentSigningMethodSelectOptions: Array<Option>;

  banklinkOptions: BanklinkOptions;
  spouseConsentPageViewType: string;
  smartIdSpouseSignaturePollChallengeId: string;
  mobileIdSpouseSignaturePollChallengeId: string;
  signAppByMobileIdOrSmartId: () => Promise<void>;
};

export type HirePurchaseSuccessPageLogic = {
  processingSuccessPage: boolean;
  isWithDownPayment: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  returnToMerchantButtonDisabled: boolean;
  onReturnToMerchantButtonClick: () => void;
  onCustomerProfileRedirectionButtonClick: () => void;
  onEstoAccountButtonClick: () => void;
  userCreditAccount: Nullable<MeCreditAccountFragment>;
  shouldShowDisclaimerWithCreditAmount: boolean;
  everyPayProviderEnabled: boolean;
};

export type SmallLoanSuccessPageLogic = {
  isInstantPayout: boolean;
  processingSuccessPage: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  onCustomerProfileRedirectionButtonClick: () => void;
  onEstoAccountButtonClick: () => void;
  merchantName?: string;
  smallLoanIsPaidToMerchant?: boolean;
  everyPayProviderEnabled: boolean;
};

export type CreditLineSuccessPageLogic = {
  onEstoAccountButtonClick: () => void;
  isRedirectingToCustomerProfile: boolean;
  isRedirectingToCreditAccountWithdrawal: boolean;
  onWithdrawButtonClick: () => void;
};

export type PendingPageLogic = {
  processingPendingPage?: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  debtAmount?: number | undefined;
  onPayDebtButtonClick?: () => void;
  pageUrlAndNavigationProcessing: boolean;
  onEstoAccountButtonClick: () => void;
  onCheckIncomeButtonClick: () => void;
  onCreditLineOnboardingButtonClick?: () => void;
  onCreditLineWithdrawalOnboardingButtonClick?: () => void;
  isRedirecting?: boolean;
  availableCreditLimit?: number;
  creditLimit?: number;
};

export type SmallLoanRejectPageLogic = {
  processingRejectPage: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  returnToMerchantButtonDisabled: boolean;
  onReturnToMerchantButtonClick: () => void;
  onGoToFAQButtonClick: () => void;
  onEstoAccountButtonClick: () => void;
  headingLabel: string;
  disclaimerLabel: string;
  onForwardLoanClick: () => void;
};

export type HirePurchaseRejectPageLogic = {
  processingRejectPage: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  returnToMerchantButtonDisabled: boolean;
  onReturnToMerchantButtonClick: () => void;
  onGoToFAQButtonClick: () => void;
  headingLabel: string;
  disclaimerLabel: string;
};

export type CreditLineRejectPageLogic = {
  isForwardLoadLinkProcessing: boolean;
  isRedirectingToCustomerProfile: boolean;
  visiblePageAttributes: VisiblePageAttributes;
  onGoToFAQButtonClick: () => void;
  onEstoAccountButtonClick: () => void;
  onForwardLoanClick: () => void;
};

export type EmtaConsentPageLogic = {
  returnedFromEmtaService: boolean;
  emtaConsentPageProcessing: boolean;
  onContinueButtonClick: () => void;
  onEmtaRedirectButtonClick: () => void;
  onAccountScoringRedirectButtonClick: () => void;
  incomeSourcesCheckServicesOptions: Array<IncomeSourcesCheckServiceOption>;
};

export type SchedulePageLogic = {
  selectedScheduleType: ApplicationScheduleType | null;
  scheduleOptions: Array<{
    scheduleType: ApplicationScheduleType;
    img: string;
    title: string;
    description: string;
    shouldShow: boolean;
    id: string;
  }>;
  loadedScheduleImages: Array<ApplicationScheduleType>;
  schedulePageLoaded: boolean;
  continueButtonDisabled: boolean;
  onContinueButtonClick: () => void;
  onScheduleImageLoad: (scheduleType: ApplicationScheduleType) => () => void;
  onScheduleItemClick: (scheduleType: ApplicationScheduleType) => () => void;
};

export type AfterDirectPaymentPageLogic = {
  userEmail: string | null;
  paymentStatus: KlixPaymentStatusType | StripePaymentStatusType | null;
  merchantUrl: string | null;
  retryUrl: string | null;
  pageLoaded: boolean;
  pageTitle: string;
};

export type AppLoginButtonsLogic = {
  loginMethodButtons: Array<AppButtonProps & { method: AppLoginMethods }>;
  selectedLoginMethod: AppLoginMethods;
};
